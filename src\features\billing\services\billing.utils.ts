import type {
  ProjectSubscription,
  SubscriptionWithAccess,
  SubscriptionStatus,
  BillingSummary,
  PaymentRetryConfig,
  BillingNotification,
  BillingNotificationType,
} from '@/types/billing';
import {
  BILLING_CONSTANTS,
  getDaysRemainingInGracePeriod,
  convertMyrToCents,
} from '@/types/billing';

// ================================
// MALAYSIAN TIMEZONE AND DATE UTILITIES
// ================================

export const MALAYSIA_TIMEZONE = 'Asia/Kuala_Lumpur';

export function getCurrentMalaysianTime(): Date {
  return new Date(
    new Date().toLocaleString('en-US', { timeZone: MALAYSIA_TIMEZONE }),
  );
}

export function formatDateForMalaysia(date: Date): string {
  return date.toLocaleDateString('en-MY', {
    timeZone: MALAYSIA_TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
}

export function formatDateTimeForMalaysia(date: Date): string {
  return date.toLocaleString('en-MY', {
    timeZone: MALAYSIA_TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
}

// ================================
// ENHANCED BILLING DATE CALCULATIONS
// ================================

export function calculateNextBillingDateMalaysian(currentDate?: Date): Date {
  const baseDate = currentDate || getCurrentMalaysianTime();
  const nextBilling = new Date(baseDate);
  nextBilling.setMonth(nextBilling.getMonth() + 1);

  // Ensure we're working in Malaysian timezone
  const malaysianDate = new Date(
    nextBilling.toLocaleString('en-US', { timeZone: MALAYSIA_TIMEZONE }),
  );

  return malaysianDate;
}

export function calculateGracePeriodEndMalaysian(
  failureDate?: Date,
  gracePeriodDays?: number,
): Date {
  const baseDate = failureDate || getCurrentMalaysianTime();
  const gracePeriod = gracePeriodDays || BILLING_CONSTANTS.GRACE_PERIOD_DAYS;

  const gracePeriodEnd = new Date(baseDate);
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriod);

  // Ensure we're working in Malaysian timezone
  const malaysianDate = new Date(
    gracePeriodEnd.toLocaleString('en-US', { timeZone: MALAYSIA_TIMEZONE }),
  );

  return malaysianDate;
}

export function isDateInMalaysianBusinessHours(date: Date): boolean {
  const malaysianDate = new Date(
    date.toLocaleString('en-US', { timeZone: MALAYSIA_TIMEZONE }),
  );
  const hour = malaysianDate.getHours();
  const day = malaysianDate.getDay();

  // Business hours: 9 AM to 6 PM, Monday to Friday
  return day >= 1 && day <= 5 && hour >= 9 && hour < 18;
}

// ================================
// ENHANCED ACCESS STATE DETERMINATION
// ================================

export interface DetailedAccessState {
  hasAccess: boolean;
  status: SubscriptionStatus;
  reason: string;
  severity: 'info' | 'warning' | 'error';
  actionRequired: boolean;
  nextAction?: string;
  daysRemaining?: number;
  nextBillingDate?: Date;
  gracePeriodEnd?: Date;
}

export function determineDetailedAccessState(
  subscription: ProjectSubscription,
): DetailedAccessState {
  const status = subscription.status;
  const currentTime = getCurrentMalaysianTime();

  const baseState: DetailedAccessState = {
    hasAccess: false,
    status,
    reason: '',
    severity: 'error',
    actionRequired: true,
  };

  switch (status) {
    case 'active':
      return {
        ...baseState,
        hasAccess: true,
        reason: 'Subscription is active',
        severity: 'info',
        actionRequired: false,
        nextBillingDate: subscription.next_billing_date
          ? new Date(subscription.next_billing_date)
          : undefined,
      };

    case 'grace_period':
      const gracePeriodEnd = subscription.grace_period_ends
        ? new Date(subscription.grace_period_ends)
        : null;
      const isGracePeriodValid = gracePeriodEnd && gracePeriodEnd > currentTime;
      const daysRemaining = gracePeriodEnd
        ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
        : 0;

      return {
        ...baseState,
        hasAccess: isGracePeriodValid || false,
        reason: isGracePeriodValid
          ? `Grace period active - ${daysRemaining} days remaining`
          : 'Grace period has expired',
        severity: isGracePeriodValid ? 'warning' : 'error',
        actionRequired: true,
        nextAction: 'Make payment to restore access',
        daysRemaining: daysRemaining || undefined,
        gracePeriodEnd: gracePeriodEnd || undefined,
      };

    case 'pending_payment':
      return {
        ...baseState,
        hasAccess: false,
        reason: 'Payment is pending',
        severity: 'warning',
        actionRequired: true,
        nextAction: 'Complete pending payment',
        nextBillingDate: subscription.next_billing_date
          ? new Date(subscription.next_billing_date)
          : undefined,
      };

    case 'suspended':
      return {
        ...baseState,
        hasAccess: false,
        reason: 'Subscription has been suspended',
        severity: 'error',
        actionRequired: true,
        nextAction: 'Contact support or make payment',
      };

    case 'cancelled':
      return {
        ...baseState,
        hasAccess: false,
        reason: 'Subscription has been cancelled',
        severity: 'error',
        actionRequired: true,
        nextAction: 'Reactivate subscription',
      };

    default:
      return {
        ...baseState,
        hasAccess: false,
        reason: `Unknown subscription status: ${status}`,
        severity: 'error',
        actionRequired: true,
        nextAction: 'Contact support',
      };
  }
}

export function enhanceSubscriptionWithAccess(
  subscription: ProjectSubscription,
): SubscriptionWithAccess {
  const accessState = determineDetailedAccessState(subscription);

  return {
    ...subscription,
    accessAllowed: accessState.hasAccess,
    isInGracePeriod: subscription.status === 'grace_period',
    daysUntilSuspension: accessState.daysRemaining,
  };
}

// ================================
// AMOUNT CALCULATIONS AND VALIDATIONS
// ================================

export interface AmountValidationResult {
  isValid: boolean;
  errors: string[];
  formattedAmount?: string;
  convertedCents?: number;
}

export function validateAndFormatAmount(
  amount: number,
  currency: string = 'MYR',
): AmountValidationResult {
  const errors: string[] = [];

  // Basic validation
  if (typeof amount !== 'number' || isNaN(amount)) {
    errors.push('Amount must be a valid number');
  }

  if (amount <= 0) {
    errors.push('Amount must be greater than zero');
  }

  if (amount > 99999999) {
    // BillPlz limit
    errors.push('Amount exceeds maximum limit');
  }

  // Currency specific validation
  if (currency === 'MYR') {
    if (amount < 1) {
      errors.push('Minimum amount is RM 1.00');
    }

    // Check for more than 2 decimal places
    const decimalPlaces = (amount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
      errors.push('Amount cannot have more than 2 decimal places');
    }
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  // Format amount
  const formattedAmount =
    currency === 'MYR'
      ? `RM ${amount.toFixed(2)}`
      : `${amount.toFixed(2)} ${currency}`;

  const convertedCents =
    currency === 'MYR' ? convertMyrToCents(amount) : Math.round(amount * 100);

  return {
    isValid: true,
    errors: [],
    formattedAmount,
    convertedCents,
  };
}

export function calculateProRatedAmount(
  monthlyAmount: number,
  startDate: Date,
  endDate: Date,
): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const daysInPeriod = Math.ceil(
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
  );
  const daysInMonth = new Date(
    start.getFullYear(),
    start.getMonth() + 1,
    0,
  ).getDate();

  const proRatedAmount = (monthlyAmount / daysInMonth) * daysInPeriod;

  return Math.round(proRatedAmount * 100) / 100; // Round to 2 decimal places
}

export function calculateLateFees(
  baseAmount: number,
  daysLate: number,
  lateFeePercentage: number = 0.05, // 5% default
): number {
  if (daysLate <= 0) return 0;

  const lateFee = baseAmount * (lateFeePercentage * Math.min(daysLate / 30, 3)); // Max 3 months
  return Math.round(lateFee * 100) / 100;
}

// ================================
// BILLING SUMMARY CALCULATIONS
// ================================

export function calculateBillingSummary(
  subscriptions: SubscriptionWithAccess[],
): BillingSummary {
  const summary: BillingSummary = {
    totalActiveSubscriptions: 0,
    totalMonthlyAmount: 0,
    upcomingPayments: 0,
    overduePayments: 0,
    subscriptionsInGracePeriod: 0,
  };

  const currentTime = getCurrentMalaysianTime();
  const nextWeek = new Date(currentTime);
  nextWeek.setDate(nextWeek.getDate() + 7);

  for (const subscription of subscriptions) {
    if (subscription.status === 'active') {
      summary.totalActiveSubscriptions++;
      summary.totalMonthlyAmount += subscription.amount;

      // Check for upcoming payments
      if (subscription.next_billing_date) {
        const nextBilling = new Date(subscription.next_billing_date);
        if (nextBilling <= nextWeek) {
          summary.upcomingPayments++;
        }

        // Check for overdue payments
        if (nextBilling < currentTime) {
          summary.overduePayments++;
        }
      }
    }

    if (subscription.isInGracePeriod) {
      summary.subscriptionsInGracePeriod++;
    }
  }

  return summary;
}

// ================================
// PAYMENT RETRY LOGIC
// ================================

export function calculateNextRetryDate(
  lastAttempt: Date,
  attemptCount: number,
  config: PaymentRetryConfig = {
    maxRetries: BILLING_CONSTANTS.PAYMENT_RETRY.MAX_RETRIES,
    retryIntervalDays: BILLING_CONSTANTS.PAYMENT_RETRY.RETRY_INTERVAL_DAYS,
    gracePeriodDays: BILLING_CONSTANTS.GRACE_PERIOD_DAYS,
  },
): Date | null {
  if (attemptCount >= config.maxRetries) {
    return null; // Max retries reached
  }

  const nextRetry = new Date(lastAttempt);
  const backoffDays = config.retryIntervalDays * Math.pow(2, attemptCount); // Exponential backoff

  nextRetry.setDate(nextRetry.getDate() + Math.min(backoffDays, 14)); // Max 14 days between retries

  return nextRetry;
}

export function shouldRetryPayment(
  lastAttempt: Date,
  attemptCount: number,
  config: PaymentRetryConfig = {
    maxRetries: BILLING_CONSTANTS.PAYMENT_RETRY.MAX_RETRIES,
    retryIntervalDays: BILLING_CONSTANTS.PAYMENT_RETRY.RETRY_INTERVAL_DAYS,
    gracePeriodDays: BILLING_CONSTANTS.GRACE_PERIOD_DAYS,
  },
): boolean {
  if (attemptCount >= config.maxRetries) {
    return false;
  }

  const nextRetryDate = calculateNextRetryDate(
    lastAttempt,
    attemptCount,
    config,
  );
  if (!nextRetryDate) {
    return false;
  }

  return getCurrentMalaysianTime() >= nextRetryDate;
}

// ================================
// BILLING NOTIFICATIONS
// ================================

export function generateBillingNotification(
  subscription: SubscriptionWithAccess,
  type: BillingNotificationType,
): BillingNotification {
  const accessState = determineDetailedAccessState(subscription);

  const baseNotification: BillingNotification = {
    type,
    subscriptionId: subscription.id,
    projectId: subscription.project_id,
    contractorId: subscription.contractor_id,
    message: '',
    actionRequired: false,
  };

  switch (type) {
    case 'payment_due':
      return {
        ...baseNotification,
        message: `Payment due for subscription ${subscription.id}`,
        actionRequired: true,
        dueDate: subscription.next_billing_date || undefined,
      };

    case 'payment_failed':
      return {
        ...baseNotification,
        message: `Payment failed for subscription ${subscription.id}. Grace period activated.`,
        actionRequired: true,
        dueDate: accessState.gracePeriodEnd?.toISOString(),
      };

    case 'grace_period_started':
      return {
        ...baseNotification,
        message: `Grace period started for subscription ${subscription.id}. ${accessState.daysRemaining} days remaining.`,
        actionRequired: true,
        dueDate: accessState.gracePeriodEnd?.toISOString(),
      };

    case 'grace_period_ending':
      return {
        ...baseNotification,
        message: `Grace period ending soon for subscription ${subscription.id}. ${accessState.daysRemaining} days remaining.`,
        actionRequired: true,
        dueDate: accessState.gracePeriodEnd?.toISOString(),
      };

    case 'subscription_suspended':
      return {
        ...baseNotification,
        message: `Subscription ${subscription.id} has been suspended due to non-payment.`,
        actionRequired: true,
      };

    case 'payment_successful':
      return {
        ...baseNotification,
        message: `Payment successful for subscription ${subscription.id}. Access restored.`,
        actionRequired: false,
      };

    default:
      return {
        ...baseNotification,
        message: `Billing notification for subscription ${subscription.id}`,
        actionRequired: false,
      };
  }
}

export function getNotificationPriority(
  type: BillingNotificationType,
): 'low' | 'medium' | 'high' | 'urgent' {
  switch (type) {
    case 'subscription_suspended':
      return 'urgent';
    case 'payment_failed':
    case 'grace_period_ending':
      return 'high';
    case 'payment_due':
    case 'grace_period_started':
      return 'medium';
    case 'payment_successful':
      return 'low';
    default:
      return 'medium';
  }
}

// ================================
// UTILITY FUNCTIONS FOR BULK OPERATIONS
// ================================

export function groupSubscriptionsByStatus(
  subscriptions: SubscriptionWithAccess[],
): Record<SubscriptionStatus, SubscriptionWithAccess[]> {
  const groups: Record<SubscriptionStatus, SubscriptionWithAccess[]> = {
    active: [],
    pending_payment: [],
    grace_period: [],
    suspended: [],
    cancelled: [],
  };

  for (const subscription of subscriptions) {
    groups[subscription.status].push(subscription);
  }

  return groups;
}

export function filterSubscriptionsByAccessStatus(
  subscriptions: SubscriptionWithAccess[],
  hasAccess: boolean,
): SubscriptionWithAccess[] {
  return subscriptions.filter((sub) => sub.accessAllowed === hasAccess);
}

export function sortSubscriptionsByNextBilling(
  subscriptions: SubscriptionWithAccess[],
  ascending: boolean = true,
): SubscriptionWithAccess[] {
  return [...subscriptions].sort((a, b) => {
    const dateA = a.next_billing_date
      ? new Date(a.next_billing_date).getTime()
      : 0;
    const dateB = b.next_billing_date
      ? new Date(b.next_billing_date).getTime()
      : 0;

    return ascending ? dateA - dateB : dateB - dateA;
  });
}

export function getSubscriptionsRequiringAttention(
  subscriptions: SubscriptionWithAccess[],
): SubscriptionWithAccess[] {
  const currentTime = getCurrentMalaysianTime();

  return subscriptions.filter((sub) => {
    // Overdue payments
    if (
      sub.next_billing_date &&
      new Date(sub.next_billing_date) < currentTime
    ) {
      return true;
    }

    // Grace period ending soon (within 2 days)
    if (
      sub.isInGracePeriod &&
      sub.daysUntilSuspension !== undefined &&
      sub.daysUntilSuspension <= 2
    ) {
      return true;
    }

    // Suspended or cancelled
    if (sub.status === 'suspended' || sub.status === 'cancelled') {
      return true;
    }

    return false;
  });
}

// ================================
// EXPORT ALL UTILITIES
// ================================

export const billingUtils = {
  // Date utilities
  getCurrentMalaysianTime,
  formatDateForMalaysia,
  formatDateTimeForMalaysia,
  calculateNextBillingDateMalaysian,
  calculateGracePeriodEndMalaysian,
  isDateInMalaysianBusinessHours,

  // Access state utilities
  determineDetailedAccessState,
  enhanceSubscriptionWithAccess,

  // Amount utilities
  validateAndFormatAmount,
  calculateProRatedAmount,
  calculateLateFees,

  // Summary utilities
  calculateBillingSummary,

  // Retry utilities
  calculateNextRetryDate,
  shouldRetryPayment,

  // Notification utilities
  generateBillingNotification,
  getNotificationPriority,

  // Bulk operation utilities
  groupSubscriptionsByStatus,
  filterSubscriptionsByAccessStatus,
  sortSubscriptionsByNextBilling,
  getSubscriptionsRequiringAttention,
};
