'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useComplaint,
  useUpdateComplaintStatus,
} from '@/features/complaints/hooks/use-complaints-simple';
import { useUserWithProfile } from '@/hooks/use-auth';
import { format } from 'date-fns';
import {
  AlertCircle,
  ArrowLeft,
  Calendar as CalendarIcon,
  CheckCircle,
  Clock,
  FileText,
  MapPin,
  Shield,
  User,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export default function ComplaintDetailPage() {
  const router = useRouter();
  const params = useParams();
  const complaintId = params.id as string;
  const t = useTranslations('complaints');
  const tCommon = useTranslations('common');

  const [isVerifyDialogOpen, setIsVerifyDialogOpen] = useState(false);

  const { data: complaint, isLoading, error } = useComplaint(complaintId);
  const { data: user } = useUserWithProfile();
  const updateComplaintMutation = useUpdateComplaintStatus();

  // Check if user is admin
  const isAdmin = user?.profile?.user_role === 'admin';

  const handleVerifyComplaint = () => {
    if (!user?.profile?.name) {
      toast.error('User information not available');
      return;
    }

    // Use current date for verification
    const currentDate = new Date();

    updateComplaintMutation.mutate(
      {
        id: complaintId,
        follow_up: 'verified',
        verified_by: user.profile.name,
        verified_date: format(currentDate, 'yyyy-MM-dd'),
      },
      {
        onSuccess: () => {
          setIsVerifyDialogOpen(false);
          router.back();
          toast.success(t('actions.verifySuccess'));
        },
        onError: () => {
          setIsVerifyDialogOpen(false);
          toast.error(t('actions.verifyError'));
        },
      },
    );
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaint details...
          </div>
        </div>
      </div>
    );
  }

  if (error || !complaint) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaint details</p>
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-3 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="self-start"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Complaint Details</h1>
            <p className="text-sm sm:text-base text-gray-600">
              Report ID: {complaint.number}
            </p>
          </div>
        </div>

        {/* Verify Button - Only show for admin if not already verified and has completion date */}
        {isAdmin &&
          complaint.follow_up !== 'verified' &&
          complaint.actual_completion_date && (
            <AlertDialog
              open={isVerifyDialogOpen}
              onOpenChange={setIsVerifyDialogOpen}
            >
              <AlertDialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700 text-white">
                  <Shield className="h-4 w-4 mr-2" />
                  {t('verification.verifyButton')}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t('verification.title')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('verification.description')}
                  </AlertDialogDescription>
                </AlertDialogHeader>

                <div className="py-4">
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    {t('verification.dateLabel')}
                  </label>
                  <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                    <CalendarIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-900">
                      {format(new Date(), 'PPP')} (Today)
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Verification will be recorded with today&apos;s date
                  </p>
                </div>

                <AlertDialogFooter>
                  <AlertDialogCancel>{tCommon('cancel')}</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleVerifyComplaint}
                    disabled={updateComplaintMutation.isPending}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {updateComplaintMutation.isPending
                      ? t('verification.verifying')
                      : t('actions.verify')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Section A - Damage Complaint Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Damage Complaint Form (Open Ticket)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Email
                </label>
                <p className="text-sm text-gray-900">{complaint.email}</p>
              </div>{' '}
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Date of Damage
                </label>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">
                    {new Date(complaint.date).toLocaleDateString()}
                  </p>
                </div>
              </div>{' '}
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Expected Completion Date
                </label>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">
                    {complaint.expected_completion_date
                      ? new Date(
                          complaint.expected_completion_date,
                        ).toLocaleDateString()
                      : 'Not set'}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Contractor Name
                </label>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">
                    {complaint.contractor_name || 'N/A'}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Location
                </label>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">
                    {complaint.location || 'N/A'}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  PMA/LIF Number
                </label>
                <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                  {complaint.no_pma_lif || 'N/A'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Description
                </label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded">
                  {complaint.description || 'No description provided'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Involves Mantrap
                </label>
                <p className="text-sm text-gray-900">
                  {complaint.involves_mantrap ? 'Yes' : 'No'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Before Repair Photos
                </label>
                {complaint.before_repair_files &&
                complaint.before_repair_files.length > 0 ? (
                  <div className="space-y-2">
                    {complaint.before_repair_files.map((fileUrl, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <a
                          href={fileUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          Before Repair Photo {index + 1}
                        </a>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    No before repair photos uploaded
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Section B - Repair Information Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Repair Information Form (Closed Ticket)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {complaint.actual_completion_date ? (
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Actual Completion Date
                  </label>
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-gray-500" />
                    <p className="text-sm text-gray-900">
                      {new Date(
                        complaint.actual_completion_date,
                      ).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Repair Completion Time
                  </label>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <p className="text-sm text-gray-900">
                      {complaint.repair_completion_time || 'N/A'}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Cause of Damage
                  </label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded">
                    {complaint.cause_of_damage || 'Not specified'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Correction Action
                  </label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded">
                    {complaint.correction_action || 'Not specified'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Repair Cost
                  </label>
                  <p className="text-sm text-gray-900">
                    {complaint.repair_cost
                      ? `RM ${complaint.repair_cost.toFixed(2)}`
                      : 'Not specified'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Proof of Repair
                  </label>
                  {complaint.proof_of_repair_urls &&
                  complaint.proof_of_repair_urls.length > 0 ? (
                    <div className="space-y-2">
                      {complaint.proof_of_repair_urls.map((url, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm underline"
                          >
                            Repair Evidence {index + 1}
                          </a>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No proof of repair uploaded
                    </p>
                  )}
                </div>

                {/* Verification Fields */}
                <div className="border-t pt-4 mt-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    {t('verification.verificationDetails')}
                  </h4>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('verification.verifiedBy')}
                      </label>
                      <p className="text-sm text-gray-900">
                        {complaint.verified_by || '-'}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('verification.verifiedDate')}
                      </label>
                      <p className="text-sm text-gray-900">
                        {complaint.verified_date
                          ? new Date(
                              complaint.verified_date,
                            ).toLocaleDateString()
                          : '-'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  Repair information not yet submitted
                </p>
                <p className="text-sm text-gray-400">
                  Section B will be available once the contractor submits repair
                  details
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
