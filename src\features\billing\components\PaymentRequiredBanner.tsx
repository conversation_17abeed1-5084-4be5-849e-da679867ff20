'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import {
  AlertTriangle,
  Calendar,
  ChevronRight,
  Clock,
  CreditCard,
  DollarSign,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface PaymentRequiredBannerProps {
  subscription: SubscriptionWithAccess;
  projectName: string;
  onPayNow?: () => void;
  onDismiss?: () => void;
  className?: string;
  variant?: 'banner' | 'card' | 'alert';
  showDismiss?: boolean;
}

/**
 * Prominent banner for pending_payment status projects with clear call-to-action
 */
export function PaymentRequiredBanner({
  subscription,
  projectName,
  onPayNow,
  onDismiss,
  className,
  variant = 'banner',
  showDismiss = true,
}: PaymentRequiredBannerProps) {
  const [isDismissed, setIsDismissed] = useState(false);
  const monthlyAmount = subscription.amount || 0;

  const nextBillingDate = subscription.next_billing_date
    ? new Date(subscription.next_billing_date)
    : null;

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  if (isDismissed) {
    return null;
  }

  // Alert variant - inline with content
  if (variant === 'alert') {
    return (
      <Alert
        className={cn(
          'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
          className,
        )}
      >
        <CreditCard className="h-4 w-4 text-blue-600" />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-blue-800 dark:text-blue-200">
                Payment Required for {projectName}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Monthly subscription: RM {monthlyAmount.toFixed(2)}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {onPayNow && (
                <Button
                  size="sm"
                  onClick={onPayNow}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Pay Now
                </Button>
              )}
              {showDismiss && onDismiss && (
                <Button size="sm" variant="ghost" onClick={handleDismiss}>
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Card variant - standalone card
  if (variant === 'card') {
    return (
      <Card
        className={cn(
          'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
          className,
        )}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <CreditCard className="h-6 w-6 text-blue-600" />
              </div>
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                    Payment Required
                  </h3>
                  <p className="text-blue-700 dark:text-blue-300">
                    Complete payment to activate access for{' '}
                    <strong>{projectName}</strong>
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-blue-600 font-medium">
                        Monthly Amount
                      </p>
                      <p className="text-blue-800 dark:text-blue-200">
                        RM {monthlyAmount.toFixed(2)}
                      </p>
                    </div>
                  </div>
                  {nextBillingDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <div>
                        <p className="text-blue-600 font-medium">Due Date</p>
                        <p className="text-blue-800 dark:text-blue-200">
                          {nextBillingDate.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {onPayNow && (
                  <Button
                    onClick={onPayNow}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Set Up Payment
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <ProjectAccessIndicator
                status={subscription.status}
                gracePeriodEnds={subscription.grace_period_ends}
                size="sm"
              />
              {showDismiss && onDismiss && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleDismiss}
                  className="text-blue-600 hover:text-blue-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Banner variant (default) - full-width prominent banner
  return (
    <div
      className={cn(
        'w-full border-b border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 dark:border-blue-800',
        className,
      )}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <CreditCard className="h-5 w-5 text-blue-600" />
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                  Payment Required for {projectName}
                </h3>
                <Badge
                  variant="outline"
                  className="text-blue-700 border-blue-300"
                >
                  Pending Payment
                </Badge>
              </div>

              <div className="flex items-center gap-6 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  <span>RM {monthlyAmount.toFixed(2)}/month</span>
                </div>
                {nextBillingDate && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Due {nextBillingDate.toLocaleDateString()}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  <span>Project access suspended until payment</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {onPayNow && (
              <Button
                onClick={onPayNow}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Pay Now
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            )}

            {showDismiss && onDismiss && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDismiss}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
