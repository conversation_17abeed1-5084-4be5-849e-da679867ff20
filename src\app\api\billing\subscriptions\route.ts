import { NextRequest, NextResponse } from 'next/server';
import { getUser } from '@/lib/supabase-server';
import { hasPermission } from '@/lib/rbac';
import { subscriptionsService } from '@/features/billing/services/subscriptions.service';
import { supabase } from '@/lib/supabase';
import { z } from 'zod';
import type { SubscriptionStatus } from '@/types/billing';

const createSubscriptionSchema = z.object({
  projectId: z.string().uuid(),
  contractorId: z.string().uuid(),
  status: z
    .enum([
      'active',
      'pending_payment',
      'grace_period',
      'suspended',
      'cancelled',
    ])
    .optional(),
  nextBillingDate: z.string().datetime().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile for role checking
    const { data: userProfile } = await supabase
      .from('users')
      .select('user_role')
      .eq('id', user.id)
      .single();

    if (!userProfile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 },
      );
    }

    if (!hasPermission(userProfile.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const contractorId = searchParams.get('contractorId');
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const accessAllowed = searchParams.get('accessAllowed');

    // Validate contractor access for non-admin users
    if (
      userProfile.user_role === 'contractor' &&
      contractorId &&
      contractorId !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot access other contractor subscriptions' },
        { status: 403 },
      );
    }

    // If contractor is not specified, use current user for contractors
    const effectiveContractorId =
      userProfile.user_role === 'contractor' ? user.id : contractorId;

    const filters = {
      contractorId: effectiveContractorId || undefined,
      projectId: projectId || undefined,
      status: (status as SubscriptionStatus) || undefined,
      accessAllowed:
        accessAllowed === 'true'
          ? true
          : accessAllowed === 'false'
            ? false
            : undefined,
    };

    const result = await subscriptionsService.getAllWithFilters(filters);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      data: result.data,
      meta: {
        count: result.data.length,
        filters: filters,
      },
    });
  } catch (error) {
    console.error('GET /api/billing/subscriptions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile for role checking
    const { data: userProfile } = await supabase
      .from('users')
      .select('user_role')
      .eq('id', user.id)
      .single();

    if (!userProfile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 },
      );
    }

    if (!hasPermission(userProfile.user_role, 'projects.create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create subscriptions' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = createSubscriptionSchema.parse(body);

    // Contractors can only create subscriptions for themselves
    if (
      userProfile.user_role === 'contractor' &&
      validatedData.contractorId !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot create subscription for other contractors' },
        { status: 403 },
      );
    }

    // Check if subscription already exists for this project
    const existingSubscription = await subscriptionsService.getByProjectId(
      validatedData.projectId,
    );

    if (existingSubscription.data) {
      return NextResponse.json(
        { error: 'Subscription already exists for this project' },
        { status: 409 },
      );
    }

    // Create subscription
    const result = await subscriptionsService.createSubscriptionForProject({
      projectId: validatedData.projectId,
      contractorId: validatedData.contractorId,
      status: validatedData.status,
      nextBillingDate: validatedData.nextBillingDate
        ? new Date(validatedData.nextBillingDate)
        : undefined,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Get subscription with access status
    const subscriptionWithAccess =
      await subscriptionsService.getSubscriptionWithAccess(result.data!.id);

    return NextResponse.json(
      {
        data: subscriptionWithAccess.data || result.data,
        message: 'Subscription created successfully',
      },
      { status: 201 },
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('POST /api/billing/subscriptions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
