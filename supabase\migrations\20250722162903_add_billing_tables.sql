-- ================================
-- BILLPLZ PAYMENT GATEWAY INTEGRATION
-- Phase 1: Billing Tables with Enhanced Access Control
-- ================================

-- Create subscription status enum
CREATE TYPE public.subscription_status AS ENUM (
    'active',
    'pending_payment', 
    'grace_period',
    'cancelled',
    'suspended'
);

-- Create payment status enum  
CREATE TYPE public.payment_status AS ENUM (
    'pending',
    'paid',
    'failed',
    'cancelled',
    'refunded'
);

-- ================================
-- PROJECT SUBSCRIPTIONS TABLE
-- Core subscription management with access control
-- ================================

CREATE TABLE public.project_subscriptions (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    project_id uuid NOT NULL,
    contractor_id uuid NOT NULL,
    amount decimal(10,2) DEFAULT 150.00 NOT NULL,
    currency text DEFAULT 'MYR' NOT NULL,
    status public.subscription_status DEFAULT 'pending_payment' NOT NULL,
    billing_cycle text DEFAULT 'monthly' NOT NULL,
    next_billing_date timestamp with time zone,
    grace_period_ends timestamp with time zone,
    access_allowed boolean GENERATED ALWAYS AS (
        status IN ('active', 'grace_period')
    ) STORED,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_project_subscriptions_project_id 
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_project_subscriptions_contractor_id 
        FOREIGN KEY (contractor_id) REFERENCES public.contractors(id) ON DELETE CASCADE,
        
    -- Unique constraint: one subscription per project
    CONSTRAINT uq_project_subscriptions_project_id UNIQUE (project_id),
    
    -- Business logic constraints
    CONSTRAINT chk_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_currency_valid CHECK (currency IN ('MYR')),
    CONSTRAINT chk_billing_cycle_valid CHECK (billing_cycle IN ('monthly')),
    CONSTRAINT chk_grace_period_logic CHECK (
        (status = 'grace_period' AND grace_period_ends IS NOT NULL) OR
        (status != 'grace_period' AND grace_period_ends IS NULL)
    )
);

-- ================================
-- PAYMENT RECORDS TABLE  
-- BillPlz transaction history
-- ================================

CREATE TABLE public.payment_records (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    subscription_id uuid NOT NULL,
    billplz_bill_id text UNIQUE,
    amount decimal(10,2) NOT NULL,
    status public.payment_status DEFAULT 'pending' NOT NULL,
    paid_at timestamp with time zone,
    failure_reason text,
    billplz_response jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_payment_records_subscription_id 
        FOREIGN KEY (subscription_id) REFERENCES public.project_subscriptions(id) ON DELETE CASCADE,
        
    -- Business logic constraints
    CONSTRAINT chk_payment_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_paid_at_logic CHECK (
        (status = 'paid' AND paid_at IS NOT NULL) OR
        (status != 'paid' AND paid_at IS NULL)
    )
);

-- ================================
-- PAYMENT METHODS TABLE
-- BillPlz collection settings per contractor
-- ================================

CREATE TABLE public.payment_methods (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    contractor_id uuid NOT NULL,
    billplz_collection_id text NOT NULL,
    is_default boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_payment_methods_contractor_id 
        FOREIGN KEY (contractor_id) REFERENCES public.contractors(id) ON DELETE CASCADE
);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- Project subscriptions indexes
CREATE INDEX idx_project_subscriptions_contractor_status 
    ON public.project_subscriptions(contractor_id, status);
CREATE INDEX idx_project_subscriptions_next_billing 
    ON public.project_subscriptions(next_billing_date) 
    WHERE status = 'active';
CREATE INDEX idx_project_subscriptions_grace_period 
    ON public.project_subscriptions(grace_period_ends) 
    WHERE status = 'grace_period';

-- Computed field index for fast access control queries
CREATE INDEX idx_project_subscriptions_access_allowed 
    ON public.project_subscriptions(access_allowed, contractor_id)
    WHERE access_allowed = true;

-- Payment records indexes  
CREATE INDEX idx_payment_records_subscription_status 
    ON public.payment_records(subscription_id, status);
CREATE INDEX idx_payment_records_billplz_id 
    ON public.payment_records(billplz_bill_id) 
    WHERE billplz_bill_id IS NOT NULL;
CREATE INDEX idx_payment_records_created_at 
    ON public.payment_records(created_at DESC);

-- Payment methods indexes
CREATE INDEX idx_payment_methods_contractor 
    ON public.payment_methods(contractor_id);


-- ================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for project_subscriptions
CREATE TRIGGER trigger_update_project_subscriptions_updated_at 
    BEFORE UPDATE ON public.project_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ================================
-- INITIAL DATA FOR EXISTING PROJECTS
-- Grandfathering existing projects with active subscriptions
-- ================================

-- Insert active subscriptions for all existing projects
INSERT INTO public.project_subscriptions (project_id, contractor_id, status, next_billing_date)
SELECT 
    p.id as project_id,
    p.contractor_id,
    'active'::public.subscription_status as status,
    (now() + interval '1 month')::timestamp with time zone as next_billing_date
FROM public.projects p
WHERE p.contractor_id IS NOT NULL
  AND p.deleted_at IS NULL
ON CONFLICT (project_id) DO NOTHING;

-- ================================
-- COMMENTS FOR DOCUMENTATION
-- ================================

COMMENT ON TABLE public.project_subscriptions IS 'Monthly subscription management for lift projects with access control';
COMMENT ON TABLE public.payment_records IS 'BillPlz payment transaction history and responses';
COMMENT ON TABLE public.payment_methods IS 'Contractor payment method configurations for BillPlz';

COMMENT ON COLUMN public.project_subscriptions.status IS 'Subscription status controlling project access';
COMMENT ON COLUMN public.project_subscriptions.access_allowed IS 'Computed field: true when status allows project access (active or grace_period)';
COMMENT ON COLUMN public.project_subscriptions.grace_period_ends IS 'End of 7-day grace period after payment failure';
COMMENT ON COLUMN public.payment_records.billplz_response IS 'Complete BillPlz API response for debugging';

-- ================================
-- END OF MIGRATION  
-- ================================