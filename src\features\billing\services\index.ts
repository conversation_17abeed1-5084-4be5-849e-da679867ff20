// Server-side billing services - DO NOT USE ON CLIENT SIDE
// Use API routes for client-side billing operations

// Add runtime check to prevent client-side imports
if (typeof window !== 'undefined') {
  throw new Error(
    'Billing services cannot be imported on the client side. Use API routes instead:\n' +
      '- /api/billing/subscriptions\n' +
      '- /api/billing/payments\n' +
      '- /api/billing/projects/[id]/access\n' +
      '- /api/billing/access/validate',
  );
}

// Export all billing services (SERVER-SIDE ONLY)
export {
  AccessControlService,
  accessControlService,
} from './access-control.service';
export { billingUtils } from './billing.utils';
export { PaymentsService, paymentsService } from './payments.service';
export {
  ProjectIntegrationService,
  projectIntegrationService,
} from './project-integration.service';
export {
  SubscriptionsService,
  subscriptionsService,
} from './subscriptions.service';
export { WebhookService, webhookService } from './webhook.service';

// Export service types
export type {
  CreateSubscriptionParams,
  SubscriptionFilters,
  UpdateSubscriptionAccessParams,
} from './subscriptions.service';

export type {
  AccessValidationParams,
  BulkAccessCheckParams,
  ProjectAccessResult,
  UserProjectAccess,
} from './access-control.service';

export type {
  CreatePaymentParams,
  PaymentStatusUpdate,
  PaymentSyncResult,
} from './payments.service';

export type {
  IntegrateProjectBillingParams,
  MigrationResult,
  ProjectWithBilling,
} from './project-integration.service';

export type {
  AmountValidationResult,
  DetailedAccessState,
} from './billing.utils';

export type { WebhookProcessingResult } from './webhook.service';
