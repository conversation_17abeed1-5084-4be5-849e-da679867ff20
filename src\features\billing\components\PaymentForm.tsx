'use client';

import { <PERSON>ert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard,
  ExternalLink,
  Loader2,
  <PERSON>f<PERSON><PERSON><PERSON>,
  Shield,
} from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

// Enhanced payment form schema with BillPlz requirements
const paymentFormSchema = z.object({
  amount: z
    .number()
    .positive('Amount must be positive')
    .min(1, 'Minimum amount is RM 1.00'),
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  mobile: z
    .string()
    .regex(
      /^(\+?6?01[0-46-9]-*[0-9]{7,8})|(\+?6?0[4-9]-*[0-9]{7,8})$/,
      'Invalid Malaysian mobile number',
    )
    .optional()
    .or(z.literal('')),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(200, 'Description too long'),
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

export interface PaymentFormProps {
  subscription: SubscriptionWithAccess;
  projectName?: string;
  onSubmit: (
    data: PaymentFormData,
  ) => Promise<{ success: boolean; paymentUrl?: string; error?: string }>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
  defaultValues?: Partial<PaymentFormData>;
}

/**
 * Enhanced payment form with BillPlz integration and access restoration context
 */
export function PaymentForm({
  subscription,
  projectName,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
  defaultValues,
}: PaymentFormProps) {
  const [step, setStep] = useState<'form' | 'processing' | 'success' | 'error'>(
    'form',
  );
  const [paymentUrl, setPaymentUrl] = useState<string>();
  const [error, setError] = useState<string>();

  const monthlyAmount = subscription.amount || 0;
  const daysRemaining = getDaysRemainingInGracePeriod(
    subscription.grace_period_ends,
  );
  const isUrgent =
    subscription.isInGracePeriod &&
    (daysRemaining === null || daysRemaining <= 2);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amount: monthlyAmount,
      email: defaultValues?.email || '',
      name: defaultValues?.name || '',
      mobile: defaultValues?.mobile || '',
      description: `Monthly subscription payment for ${projectName || 'project'}`,
      ...defaultValues,
    },
  });

  async function handleSubmit(data: PaymentFormData) {
    setStep('processing');
    setError(undefined);

    try {
      const result = await onSubmit(data);

      if (result.success && result.paymentUrl) {
        setPaymentUrl(result.paymentUrl);
        setStep('success');
      } else {
        setError(result.error || 'Payment setup failed');
        setStep('error');
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'An unexpected error occurred',
      );
      setStep('error');
    }
  }

  function handleRetry() {
    setStep('form');
    setError(undefined);
    setPaymentUrl(undefined);
  }

  // Processing state
  if (step === 'processing') {
    return (
      <Card className={cn('max-w-md mx-auto', className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
          </div>
          <CardTitle>Setting up Payment</CardTitle>
          <CardDescription>
            Please wait while we prepare your BillPlz payment link...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={66} className="mb-4" />
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Creating secure payment session
            </p>
            {isUrgent && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  Processing urgent payment to restore project access
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Success state
  if (step === 'success' && paymentUrl) {
    return (
      <Card className={cn('max-w-md mx-auto border-green-200', className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-green-800">Payment Ready</CardTitle>
          <CardDescription>
            Your BillPlz payment link has been created successfully
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                Secure Payment Gateway
              </span>
            </div>
            <p className="text-xs text-green-700">
              You will be redirected to BillPlz&apos;s secure payment page
            </p>
          </div>

          {isUrgent && (
            <Alert className="border-yellow-200 bg-yellow-50">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-yellow-800">
                <strong>Urgent Payment:</strong> Complete payment to restore
                project access immediately
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <Button
              onClick={() => window.open(paymentUrl, '_blank')}
              className="w-full bg-blue-600 hover:bg-blue-700"
              size="lg"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Proceed to Payment
            </Button>

            <p className="text-xs text-center text-muted-foreground">
              After payment, your project access will be automatically restored
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (step === 'error') {
    return (
      <Card className={cn('max-w-md mx-auto border-red-200', className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-red-800">Payment Setup Failed</CardTitle>
          <CardDescription>
            We encountered an issue setting up your payment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Unable to create payment link. Please try again.'}
            </AlertDescription>
          </Alert>

          <div className="flex gap-2">
            <Button onClick={handleRetry} variant="outline" className="flex-1">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            {onCancel && (
              <Button onClick={onCancel} variant="ghost" className="flex-1">
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Form state (default)
  return (
    <Card className={cn('max-w-lg mx-auto', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Setup
              {isUrgent && (
                <Badge variant="destructive" className="text-xs">
                  Urgent
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Configure payment for {projectName || 'your project'}
            </CardDescription>
          </div>
          <ProjectAccessIndicator
            status={subscription.status}
            gracePeriodEnds={subscription.grace_period_ends}
            size="sm"
          />
        </div>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Context Alert */}
            {subscription.isInGracePeriod && (
              <Alert
                className={cn(
                  'border-yellow-200 bg-yellow-50',
                  isUrgent && 'border-red-200 bg-red-50',
                )}
              >
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">
                      {isUrgent
                        ? 'Urgent Payment Required'
                        : 'Grace Period Active'}
                    </p>
                    <p className="text-sm">
                      {daysRemaining !== null && daysRemaining > 0
                        ? `Complete payment within ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''} to maintain project access`
                        : 'Complete payment to restore project access immediately'}
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Amount Field */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Amount</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                        RM
                      </span>
                      <Input
                        type="number"
                        step="0.01"
                        min="1"
                        placeholder="150.00"
                        className="pl-12"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </div>
                  </FormControl>
                  <FormDescription>Monthly subscription amount</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Billing Information */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Billing Information</h3>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Payment receipt will be sent to this email
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mobile"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Number (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="01X-XXXXXXX" {...field} />
                    </FormControl>
                    <FormDescription>
                      Malaysian mobile number for SMS notifications
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Description for this payment"
                        className="resize-none"
                        rows={2}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className={cn(
                  'flex-1',
                  isUrgent && 'bg-red-600 hover:bg-red-700',
                )}
                size="lg"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CreditCard className="h-4 w-4 mr-2" />
                )}
                {isUrgent ? 'Create Urgent Payment' : 'Create Payment Link'}
              </Button>

              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
            </div>

            {/* Security Notice */}
            <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <p className="text-xs text-muted-foreground">
                Payments are processed securely through BillPlz. Your card
                details are never stored on our servers.
              </p>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
