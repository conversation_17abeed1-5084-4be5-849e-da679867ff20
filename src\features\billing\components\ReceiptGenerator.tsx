'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { PaymentRecord, SubscriptionWithAccess } from '@/types/billing';
import { convertCentsToMyr } from '@/types/billing';
import {
  AlertTriangle,
  Building,
  CheckCircle,
  Download,
  FileText,
  Loader2,
  Mail,
  MapPin,
  Phone,
} from 'lucide-react';
import { useState } from 'react';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface ReceiptData {
  paymentRecord: PaymentRecord;
  subscription: SubscriptionWithAccess;
  projectName: string;
  contractorName: string;
  contractorEmail: string;
  contractorAddress?: string;
  accessRestored?: boolean;
}

export interface ReceiptGeneratorProps {
  receiptData: ReceiptData;
  onDownload?: (pdfBlob: Blob) => void;
  className?: string;
  language?: 'en' | 'ms';
  autoGenerate?: boolean;
}

/**
 * PDF receipt generator with project access context and multi-language support
 */
export function ReceiptGenerator({
  receiptData,
  onDownload,
  className,
  language = 'en',
  autoGenerate = false,
}: ReceiptGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPdf, setGeneratedPdf] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);

  const {
    paymentRecord,
    subscription,
    projectName,
    contractorName,
    contractorEmail,
    accessRestored,
  } = receiptData;
  const amount = convertCentsToMyr(paymentRecord.amount || 0);

  // Translations
  const translations = {
    en: {
      title: 'Payment Receipt',
      companyName: 'Bina Integrated Sdn Bhd',
      receiptNumber: 'Receipt Number',
      paymentDate: 'Payment Date',
      projectAccess: 'Project Access',
      restored: 'Restored',
      suspended: 'Suspended',
      active: 'Active',
      billedTo: 'Billed To',
      projectDetails: 'Project Details',
      paymentDetails: 'Payment Details',
      description: 'Description',
      amount: 'Amount',
      status: 'Status',
      monthlySubscription: 'Monthly Subscription',
      accessNote: 'Access Status Note',
      accessRestoredNote:
        'Project access has been successfully restored with this payment.',
      accessActiveNote:
        'Project access is active with this subscription payment.',
      thankYou: 'Thank you for your payment!',
      contactInfo: 'Contact Information',
      generatePdf: 'Generate PDF Receipt',
      downloadPdf: 'Download PDF Receipt',
      generating: 'Generating Receipt...',
      footer: 'This is a computer-generated receipt. No signature required.',
    },
    ms: {
      title: 'Resit Pembayaran',
      companyName: 'Bina Integrated Sdn Bhd',
      receiptNumber: 'Nombor Resit',
      paymentDate: 'Tarikh Pembayaran',
      projectAccess: 'Akses Projek',
      restored: 'Dipulihkan',
      suspended: 'Digantung',
      active: 'Aktif',
      billedTo: 'Ditagih Kepada',
      projectDetails: 'Butiran Projek',
      paymentDetails: 'Butiran Pembayaran',
      description: 'Keterangan',
      amount: 'Jumlah',
      status: 'Status',
      monthlySubscription: 'Langganan Bulanan',
      accessNote: 'Nota Status Akses',
      accessRestoredNote:
        'Akses projek telah berjaya dipulihkan dengan pembayaran ini.',
      accessActiveNote:
        'Akses projek adalah aktif dengan pembayaran langganan ini.',
      thankYou: 'Terima kasih atas pembayaran anda!',
      contactInfo: 'Maklumat Hubungan',
      generatePdf: 'Jana Resit PDF',
      downloadPdf: 'Muat Turun Resit PDF',
      generating: 'Menjana Resit...',
      footer:
        'Ini adalah resit yang dijana komputer. Tiada tandatangan diperlukan.',
    },
  };

  const t = translations[language];

  const generatePDF = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      // Simulate PDF generation (replace with actual PDF library like jsPDF or Puppeteer)
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock PDF blob - in real implementation, use PDF generation library
      const mockPdfContent = generateReceiptHTML();
      const blob = new Blob([mockPdfContent], { type: 'application/pdf' });

      setGeneratedPdf(blob);
      onDownload?.(blob);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateReceiptHTML = () => {
    // This would generate actual PDF content - simplified for demo
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${t.title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { text-align: center; margin-bottom: 40px; }
            .company-name { font-size: 24px; font-weight: bold; color: #333; }
            .receipt-title { font-size: 20px; margin-top: 10px; }
            .section { margin: 20px 0; }
            .row { display: flex; justify-content: space-between; margin: 10px 0; }
            .amount { font-size: 18px; font-weight: bold; }
            .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">${t.companyName}</div>
            <div class="receipt-title">${t.title}</div>
          </div>
          
          <div class="section">
            <div class="row">
              <span>${t.receiptNumber}:</span>
              <span>${paymentRecord.id}</span>
            </div>
            <div class="row">
              <span>${t.paymentDate}:</span>
              <span>${new Date(paymentRecord.created_at || '').toLocaleDateString()}</span>
            </div>
          </div>

          <div class="section">
            <h3>${t.billedTo}</h3>
            <div>${contractorName}</div>
            <div>${contractorEmail}</div>
          </div>

          <div class="section">
            <h3>${t.projectDetails}</h3>
            <div class="row">
              <span>${t.description}:</span>
              <span>${t.monthlySubscription} - ${projectName}</span>
            </div>
            <div class="row amount">
              <span>${t.amount}:</span>
              <span>RM ${amount.toFixed(2)}</span>
            </div>
          </div>

          ${
            accessRestored
              ? `
            <div class="section">
              <h3>${t.accessNote}</h3>
              <div>${t.accessRestoredNote}</div>
            </div>
          `
              : ''
          }

          <div class="footer">
            <div>${t.footer}</div>
            <div>${t.thankYou}</div>
          </div>
        </body>
      </html>
    `;
  };

  // Auto-generate on mount if requested
  useState(() => {
    if (autoGenerate) {
      generatePDF();
    }
  });

  return (
    <Card className={cn('max-w-2xl mx-auto', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t.title}
            </CardTitle>
            <CardDescription>
              {projectName} •{' '}
              {new Date(paymentRecord.created_at || '').toLocaleDateString()}
            </CardDescription>
          </div>
          <ProjectAccessIndicator
            status={subscription.status}
            gracePeriodEnds={subscription.grace_period_ends}
            size="sm"
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Company Header */}
        <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 rounded-lg">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Building className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-bold text-blue-900 dark:text-blue-100">
              {t.companyName}
            </h2>
          </div>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            Professional Construction Management Solutions
          </p>
        </div>

        {/* Receipt Details */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card className="bg-muted/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">{t.billedTo}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="font-medium">{contractorName}</p>
              <p className="text-sm text-muted-foreground">{contractorEmail}</p>
              {receiptData.contractorAddress && (
                <p className="text-sm text-muted-foreground">
                  {receiptData.contractorAddress}
                </p>
              )}
            </CardContent>
          </Card>

          <Card className="bg-muted/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">{t.paymentDetails}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t.receiptNumber}</span>
                <span className="font-mono">
                  {paymentRecord.id.slice(-8).toUpperCase()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t.paymentDate}</span>
                <span>
                  {new Date(
                    paymentRecord.created_at || '',
                  ).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t.status}</span>
                <Badge
                  variant={
                    paymentRecord.status === 'paid' ? 'default' : 'secondary'
                  }
                >
                  {paymentRecord.status}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Project & Payment Information */}
        <div className="space-y-4">
          <h3 className="font-semibold">{t.projectDetails}</h3>

          <div className="border rounded-lg p-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">{t.description}</span>
              <span className="font-medium">
                {t.monthlySubscription} - {projectName}
              </span>
            </div>

            <Separator />

            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold">{t.amount}</span>
              <span className="text-2xl font-bold">RM {amount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Access Status Information */}
        {accessRestored && (
          <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium text-green-800 dark:text-green-200">
                  {t.accessNote}
                </p>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {t.accessRestoredNote}
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Company Contact Information */}
        <Card className="bg-muted/30">
          <CardHeader className="pb-3">
            <CardTitle className="text-base">{t.contactInfo}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span>+60 3-1234 5678</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>Kuala Lumpur, Malaysia</span>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>Failed to generate PDF: {error}</AlertDescription>
          </Alert>
        )}

        {/* PDF Generation Actions */}
        <div className="flex gap-3 pt-4 border-t">
          {!generatedPdf ? (
            <Button
              onClick={generatePDF}
              disabled={isGenerating}
              className="flex-1"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <FileText className="h-4 w-4 mr-2" />
              )}
              {isGenerating ? t.generating : t.generatePdf}
            </Button>
          ) : (
            <Button
              onClick={() => {
                const url = URL.createObjectURL(generatedPdf);
                const a = document.createElement('a');
                a.href = url;
                a.download = `receipt-${paymentRecord.id.slice(-8)}.pdf`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              {t.downloadPdf}
            </Button>
          )}

          {/* Language Toggle */}
          <Button
            variant="outline"
            onClick={() => {
              // This would trigger a re-render with different language
              // Implementation depends on your state management
            }}
          >
            {language === 'en' ? 'Bahasa' : 'English'}
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground pt-4 border-t">
          <p>{t.footer}</p>
          <p className="mt-2 font-medium text-green-600">{t.thankYou}</p>
        </div>
      </CardContent>
    </Card>
  );
}
