'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import {
  AlertTriangle,
  Ban,
  ChevronRight,
  CreditCard,
  Mail,
  MessageCircle,
  Phone,
} from 'lucide-react';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface AccessSuspendedMessageProps {
  subscription: SubscriptionWithAccess;
  projectName: string;
  onRestoreAccess?: () => void;
  onContactSupport?: () => void;
  className?: string;
  variant?: 'overlay' | 'card' | 'inline';
  showSupportOptions?: boolean;
}

/**
 * Full-screen overlay for suspended/cancelled projects with restoration flow
 */
export function AccessSuspendedMessage({
  subscription,
  projectName,
  onRestoreAccess,
  onContactSupport,
  className,
  variant = 'overlay',
  showSupportOptions = true,
}: AccessSuspendedMessageProps) {
  const monthlyAmount = subscription.amount || 0;

  const getSuspensionInfo = () => {
    switch (subscription.status) {
      case 'suspended':
        return {
          title: 'Project Access Suspended',
          reason: 'Payment Failed',
          description:
            'Your project access has been temporarily suspended due to a failed payment. Complete your payment to restore immediate access.',
          icon: Ban,
          bgColor: 'bg-red-50 dark:bg-red-950',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          accentColor: 'text-red-600',
          canRestore: true,
        };
      case 'cancelled':
        return {
          title: 'Subscription Cancelled',
          reason: 'User Cancelled',
          description:
            'Your subscription has been cancelled. Reactivate your subscription to regain access to all project features.',
          icon: Ban,
          bgColor: 'bg-gray-50 dark:bg-gray-950',
          borderColor: 'border-gray-200 dark:border-gray-800',
          textColor: 'text-gray-800 dark:text-gray-200',
          accentColor: 'text-gray-600',
          canRestore: true,
        };
      default:
        return {
          title: 'Access Unavailable',
          reason: 'Unknown Status',
          description:
            'Project access is currently unavailable. Please contact support for assistance.',
          icon: AlertTriangle,
          bgColor: 'bg-gray-50 dark:bg-gray-950',
          borderColor: 'border-gray-200 dark:border-gray-800',
          textColor: 'text-gray-800 dark:text-gray-200',
          accentColor: 'text-gray-600',
          canRestore: false,
        };
    }
  };

  const suspensionInfo = getSuspensionInfo();
  const IconComponent = suspensionInfo.icon;

  // Inline variant - compact message within content
  if (variant === 'inline') {
    return (
      <Alert
        className={cn(
          suspensionInfo.bgColor,
          suspensionInfo.borderColor,
          className,
        )}
      >
        <IconComponent className={cn('h-4 w-4', suspensionInfo.accentColor)} />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <p className={cn('font-medium', suspensionInfo.textColor)}>
                {suspensionInfo.title}
              </p>
              <p
                className={cn(
                  'text-sm mt-1',
                  suspensionInfo.textColor,
                  'opacity-80',
                )}
              >
                {suspensionInfo.description}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {suspensionInfo.canRestore && onRestoreAccess && (
                <Button
                  size="sm"
                  onClick={onRestoreAccess}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Restore Access
                </Button>
              )}
              {showSupportOptions && onContactSupport && (
                <Button size="sm" variant="outline" onClick={onContactSupport}>
                  Contact Support
                </Button>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Card variant - standalone card
  if (variant === 'card') {
    return (
      <Card
        className={cn(
          suspensionInfo.bgColor,
          suspensionInfo.borderColor,
          'border-2',
          className,
        )}
      >
        <CardHeader className="text-center pb-4">
          <div className="mx-auto w-16 h-16 bg-white/50 dark:bg-black/20 rounded-full flex items-center justify-center mb-4">
            <IconComponent
              className={cn('h-8 w-8', suspensionInfo.accentColor)}
            />
          </div>
          <CardTitle className={cn('text-xl', suspensionInfo.textColor)}>
            {suspensionInfo.title}
          </CardTitle>
          <CardDescription
            className={cn(suspensionInfo.textColor, 'opacity-80')}
          >
            {projectName}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-center">
            <Badge
              variant="outline"
              className={cn('mb-3', suspensionInfo.textColor)}
            >
              {suspensionInfo.reason}
            </Badge>
            <p className={cn('text-sm', suspensionInfo.textColor)}>
              {suspensionInfo.description}
            </p>
          </div>

          {/* Subscription Details */}
          <div className="p-4 rounded-lg bg-white/30 dark:bg-black/20">
            <div className="flex items-center justify-between mb-3">
              <span
                className={cn('text-sm font-medium', suspensionInfo.textColor)}
              >
                Subscription Details
              </span>
              <ProjectAccessIndicator
                status={subscription.status}
                gracePeriodEnds={subscription.grace_period_ends}
                size="sm"
              />
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className={cn(suspensionInfo.textColor, 'opacity-80')}>
                  Monthly Amount
                </span>
                <span className={cn('font-medium', suspensionInfo.textColor)}>
                  RM {monthlyAmount.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={cn(suspensionInfo.textColor, 'opacity-80')}>
                  Status
                </span>
                <span className={cn('font-medium', suspensionInfo.textColor)}>
                  {subscription.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {suspensionInfo.canRestore && onRestoreAccess && (
              <Button
                onClick={onRestoreAccess}
                className="w-full bg-blue-600 hover:bg-blue-700"
                size="lg"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Restore Access Now
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}

            {showSupportOptions && (
              <>
                <Separator />
                <div className="space-y-2">
                  <p
                    className={cn(
                      'text-xs text-center',
                      suspensionInfo.textColor,
                      'opacity-80',
                    )}
                  >
                    Need help? Contact our support team
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onContactSupport}
                      className="flex-1"
                    >
                      <Mail className="h-4 w-4 mr-1" />
                      Email
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onContactSupport}
                      className="flex-1"
                    >
                      <MessageCircle className="h-4 w-4 mr-1" />
                      Chat
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Overlay variant (default) - full-screen modal-style overlay
  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center p-4',
        'bg-black/50 backdrop-blur-sm',
        className,
      )}
    >
      <Card
        className={cn(
          'w-full max-w-md mx-auto',
          suspensionInfo.bgColor,
          suspensionInfo.borderColor,
          'border-2 shadow-2xl',
        )}
      >
        <CardHeader className="text-center pb-4">
          <div className="mx-auto w-20 h-20 bg-white/50 dark:bg-black/20 rounded-full flex items-center justify-center mb-6">
            <IconComponent
              className={cn('h-10 w-10', suspensionInfo.accentColor)}
            />
          </div>
          <CardTitle className={cn('text-2xl mb-2', suspensionInfo.textColor)}>
            {suspensionInfo.title}
          </CardTitle>
          <CardDescription
            className={cn('text-lg', suspensionInfo.textColor, 'opacity-80')}
          >
            {projectName}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Status Badge */}
          <div className="text-center">
            <Badge
              variant="outline"
              className={cn('mb-4 text-sm px-3 py-1', suspensionInfo.textColor)}
            >
              {suspensionInfo.reason}
            </Badge>
            <p
              className={cn(
                'text-base leading-relaxed',
                suspensionInfo.textColor,
              )}
            >
              {suspensionInfo.description}
            </p>
          </div>

          {/* Subscription Summary */}
          <Card className="bg-white/20 dark:bg-black/20 border-white/30 dark:border-black/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <span className={cn('font-medium', suspensionInfo.textColor)}>
                  Subscription Details
                </span>
                <ProjectAccessIndicator
                  status={subscription.status}
                  gracePeriodEnds={subscription.grace_period_ends}
                  size="sm"
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className={cn(suspensionInfo.textColor, 'opacity-80')}>
                    Monthly Payment
                  </span>
                  <span
                    className={cn('font-semibold', suspensionInfo.textColor)}
                  >
                    RM {monthlyAmount.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className={cn(suspensionInfo.textColor, 'opacity-80')}>
                    Status
                  </span>
                  <span
                    className={cn(
                      'font-semibold uppercase',
                      suspensionInfo.textColor,
                    )}
                  >
                    {subscription.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-4">
            {suspensionInfo.canRestore && onRestoreAccess && (
              <Button
                onClick={onRestoreAccess}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                <CreditCard className="h-5 w-5 mr-2" />
                Restore Access Now
                <ChevronRight className="h-5 w-5 ml-2" />
              </Button>
            )}

            {showSupportOptions && (
              <div className="space-y-3">
                <Separator />
                <p
                  className={cn(
                    'text-sm text-center',
                    suspensionInfo.textColor,
                    'opacity-80',
                  )}
                >
                  Need assistance? Our support team is here to help
                </p>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onContactSupport}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <Mail className="h-4 w-4 mb-1" />
                    <span className="text-xs">Email</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onContactSupport}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <MessageCircle className="h-4 w-4 mb-1" />
                    <span className="text-xs">Chat</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onContactSupport}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <Phone className="h-4 w-4 mb-1" />
                    <span className="text-xs">Call</span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
