import { accessControlService } from '@/features/billing/services/access-control.service';
import { projectIntegrationService } from '@/features/billing/services/project-integration.service';
import { subscriptionsService } from '@/features/billing/services/subscriptions.service';
import { hasPermission } from '@/lib/rbac';
import { getUser } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 },
      );
    }

    const { id: projectId } = await params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || user.id;
    const checkAsRole =
      (searchParams.get('role') as 'admin' | 'contractor' | 'viewer') ||
      user.user_role;

    // Validate project exists
    const projectResult = await projectIntegrationService.getProject(projectId);

    if (projectResult.error || !projectResult.data) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const project = projectResult.data;

    // For non-admin users, validate they can only check their own access
    if (user.user_role === 'contractor' && userId !== user.id) {
      return NextResponse.json(
        { error: 'Cannot check access for other users' },
        { status: 403 },
      );
    }

    // Check project access for the specified user
    const accessCheck = await accessControlService.checkProjectAccess(
      userId,
      projectId,
      checkAsRole,
    );

    // Get subscription details if exists
    const subscriptionResult =
      await subscriptionsService.getByProjectId(projectId);

    // Get project access state
    const accessState =
      await accessControlService.getProjectAccessState(projectId);

    // Check if user has accessible projects (for contractors)
    let userAccessibleProjects: string[] = [];
    if (checkAsRole === 'contractor') {
      const accessibleResult =
        await accessControlService.getUserAccessibleProjects(
          userId,
          checkAsRole,
        );
      userAccessibleProjects = accessibleResult.projectIds;
    }

    return NextResponse.json({
      data: {
        project: {
          id: project.id,
          name: project.name,
        },
        user: {
          id: userId,
          role: checkAsRole,
        },
        accessStatus: {
          hasAccess: accessCheck.hasAccess,
          reason: accessCheck.reason,
          message: accessCheck.message,
          gracePeriodDays: accessCheck.gracePeriodDays,
        },
        subscription: accessCheck.subscription,
        projectAccessState: accessState.data,
        userAccessibleProjects: userAccessibleProjects,
        middleware: {
          shouldRedirect:
            !accessCheck.hasAccess && checkAsRole === 'contractor',
          redirectPath:
            !accessCheck.hasAccess && checkAsRole === 'contractor'
              ? accessCheck.reason === 'no_subscription'
                ? `/billing/setup/${projectId}`
                : `/billing/payment/${subscriptionResult.data?.id || projectId}`
              : null,
          accessDenialReason: !accessCheck.hasAccess
            ? accessCheck.reason
            : null,
        },
        checkedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('GET /api/billing/projects/[id]/access error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins and the project's contractor can use this endpoint
    if (!hasPermission(user.user_role, 'projects.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to validate project access' },
        { status: 403 },
      );
    }

    const { id: projectId } = await params;
    const body = await request.json();
    const { action, userId: targetUserId, reason } = body;

    const userId = targetUserId || user.id;

    // Validate project exists
    const projectResult = await projectIntegrationService.getProject(projectId);

    if (projectResult.error || !projectResult.data) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const project = projectResult.data;

    // Get subscription for this project
    const subscriptionResult =
      await subscriptionsService.getByProjectId(projectId);

    if (!subscriptionResult.data && action !== 'check') {
      return NextResponse.json(
        { error: 'No subscription found for this project' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // For non-admin users, validate they can only act on their own subscriptions
    if (
      user.user_role === 'contractor' &&
      subscription?.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot modify other contractor project access' },
        { status: 403 },
      );
    }

    let result;
    const auditReason =
      reason || `Project access ${action} by ${user.user_role} ${user.id}`;

    switch (action) {
      case 'check':
        // Perform access validation (same as GET but with detailed context)
        const accessValidation =
          await accessControlService.validateContractorAccess({
            userId,
            userRole: 'contractor',
            projectId,
            requireActiveSubscription: true,
          });

        return NextResponse.json({
          data: {
            isValid: accessValidation.isValid,
            userProjectAccess: accessValidation.userProjectAccess,
            redirectPath: accessValidation.redirectPath,
            project: {
              id: project.id,
              name: project.name,
            },
            validatedAt: new Date().toISOString(),
          },
          message: accessValidation.isValid
            ? 'Access validated'
            : 'Access denied',
        });

      case 'restore':
        if (!subscription) {
          return NextResponse.json(
            { error: 'Cannot restore access without subscription' },
            { status: 400 },
          );
        }

        result = await accessControlService.restoreProjectAccess(
          subscription.id,
        );
        break;

      case 'revoke':
        if (!subscription) {
          return NextResponse.json(
            { error: 'Cannot revoke access without subscription' },
            { status: 400 },
          );
        }

        result = await accessControlService.revokeProjectAccess(
          subscription.id,
          auditReason,
        );
        break;

      case 'suspend':
        if (!subscription) {
          return NextResponse.json(
            { error: 'Cannot suspend access without subscription' },
            { status: 400 },
          );
        }

        const suspendResult = await subscriptionsService.suspendSubscription(
          subscription.id,
          auditReason,
        );
        result = { success: !suspendResult.error, error: suspendResult.error };
        break;

      default:
        return NextResponse.json(
          {
            error:
              'Invalid action. Supported actions: check, restore, revoke, suspend',
          },
          { status: 400 },
        );
    }

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Log access control action
    console.log(
      `Project ${projectId} access ${action} for user ${userId} by ${user.id}: ${auditReason}`,
    );

    // Get updated access status
    const updatedAccessCheck = await accessControlService.checkProjectAccess(
      userId,
      projectId,
      'contractor',
    );

    return NextResponse.json({
      data: {
        action,
        success: true,
        reason: auditReason,
        project: {
          id: project.id,
          name: project.name,
        },
        updatedAccess: {
          hasAccess: updatedAccessCheck.hasAccess,
          reason: updatedAccessCheck.reason,
          message: updatedAccessCheck.message,
        },
        subscription: updatedAccessCheck.subscription,
        timestamp: new Date().toISOString(),
      },
      message: `Project access ${action} completed successfully`,
    });
  } catch (error) {
    console.error('POST /api/billing/projects/[id]/access error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
