'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { useSignUp } from '../hooks/use-auth';
import { createRegisterSchema, type RegisterFormValues } from '../schemas';

interface ContractorRegisterFormProps
  extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function ContractorRegisterForm({
  ...props
}: ContractorRegisterFormProps) {
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const signUpMutation = useSignUp();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  // Use schema from schemas file
  const registerSchema = createRegisterSchema(validation, auth);
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      role: 'contractor', // Pre-set to contractor role
    },
  });

  const onSubmit: SubmitHandler<RegisterFormValues> = async (values) => {
    try {
      await signUpMutation.mutateAsync({
        email: values.email,
        password: values.password,
        role: 'contractor', // Ensure contractor role is always used
      });

      // The useSignUp hook will handle the redirect to check-email page
      // No need for toast messages here as the redirect provides better UX
    } catch (error: unknown) {
      // Handle specific error cases
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (errorMessage?.includes('User already registered')) {
        toast.error(auth('userExists'));
      } else if (errorMessage?.includes('Profile creation error')) {
        toast.error(auth('profileCreationError'));
      } else if (errorMessage?.includes('email_address_invalid')) {
        toast.error(validation('email'));
      } else if (errorMessage?.includes('signup_disabled')) {
        toast.error(auth('signupDisabled'));
      } else {
        // Fallback for other errors
        toast.error(errorMessage || auth('registerFailed'));
      }
    }
  };

  const isLoading = signUpMutation.isPending;

  return (
    <div className={cn('flex flex-col gap-6')} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">
          {auth('contractorRegisterTitle')}
        </h1>
        <p className="text-muted-foreground text-sm text-balance">
          {auth('contractorRegisterSubtitle')}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {signUpMutation.error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {signUpMutation.error.message}
            </div>
          )}

          <div className="grid gap-4">
            {/* Email Field */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {auth('email')} <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Password Fields */}
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {auth('password')} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          {...field}
                          className="pr-10"
                          placeholder="••••••••"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute inset-y-0 right-0 flex items-center px-3 text-muted-foreground"
                        >
                          {showPassword ? (
                            <Eye className="h-5 w-5" />
                          ) : (
                            <EyeOff className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {auth('confirmPassword')}{' '}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? 'text' : 'password'}
                          {...field}
                          className="pr-10"
                          placeholder="••••••••"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          className="absolute inset-y-0 right-0 flex items-center px-3 text-muted-foreground"
                        >
                          {showConfirmPassword ? (
                            <Eye className="h-5 w-5" />
                          ) : (
                            <EyeOff className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Contractor Information Note */}
            <div className="p-3 text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-md">
              <p className="font-medium">{auth('contractorNote')}</p>
              <p className="text-xs mt-1">{auth('contractorNoteSubtext')}</p>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {auth('creatingContractorAccount')}
                </>
              ) : (
                auth('createContractorAccount')
              )}
            </Button>
            <div className="text-center text-sm">
              {auth('alreadyHaveContractorAccount')}{' '}
              <Link
                href="/contractor/login"
                className="underline underline-offset-4"
              >
                {auth('contractorSignIn')}
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
