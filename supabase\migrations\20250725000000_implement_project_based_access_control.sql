-- ================================
-- PROJECT-BASED ACCESS CONTROL MIGRATION
-- Changes company-wide access to project-specific access
-- ================================

-- Status column already exists from previous migration
-- Ensure all existing records have accepted status
UPDATE project_users 
SET status = 'accepted' 
WHERE status IS NULL;

-- Create function to automatically add project creator to project_users
CREATE OR REPLACE FUNCTION add_project_creator_to_project_users()
RETURNS TRIGGER AS $$
BEGIN
    -- Only add if the creator is a contractor (not admin)
    IF EXISTS (
        SELECT 1 FROM users 
        WHERE id = NEW.created_by 
        AND user_role = 'contractor'
    ) THEN
        INSERT INTO project_users (
            project_id,
            user_id,
            role,
            status,
            assigned_date,
            is_active,
            created_by,
            created_at
        ) VALUES (
            NEW.id,
            NEW.created_by,
            'admin',
            'accepted',
            CURRENT_DATE,
            true,
            NEW.created_by,
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically add project creator
DROP TRIGGER IF EXISTS trg_add_project_creator ON projects;
CREATE TRIGGER trg_add_project_creator
    AFTER INSERT ON projects
    FOR EACH ROW
    EXECUTE FUNCTION add_project_creator_to_project_users();

-- Create function to check project access based on project_users table
CREATE OR REPLACE FUNCTION user_has_project_access(user_id_param uuid, project_id_param uuid)
RETURNS boolean AS $$
DECLARE
    user_record RECORD;
    has_access boolean := false;
BEGIN
    -- Get user information
    SELECT user_role, admin_access_mode, monitoring_state, contractor_id
    INTO user_record
    FROM users
    WHERE id = user_id_param AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- Admin users with project-level access can see all projects
    IF user_record.user_role = 'admin' AND user_record.admin_access_mode = 'project' THEN
        RETURN true;
    END IF;
    
    -- Admin users with state-level access can see projects in their monitoring state
    IF user_record.user_role = 'admin' AND user_record.admin_access_mode = 'state' THEN
        SELECT EXISTS (
            SELECT 1 FROM projects 
            WHERE id = project_id_param 
            AND state = user_record.monitoring_state
            AND deleted_at IS NULL
        ) INTO has_access;
        RETURN has_access;
    END IF;
    
    -- For contractors and viewers, check project_users table
    SELECT EXISTS (
        SELECT 1 FROM project_users pu
        WHERE pu.user_id = user_id_param
        AND pu.project_id = project_id_param
        AND pu.is_active = true
        AND pu.status = 'accepted'
        AND pu.deleted_at IS NULL
    ) INTO has_access;
    
    RETURN has_access;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for projects table
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view projects they have access to" ON projects;
DROP POLICY IF EXISTS "Contractors can create projects" ON projects;
DROP POLICY IF EXISTS "Users can update projects they have access to" ON projects;

-- Policy for viewing projects
CREATE POLICY "Users can view projects they have access to" ON projects
    FOR SELECT
    USING (user_has_project_access(auth.uid(), id));

-- Policy for creating projects (only contractors)
CREATE POLICY "Contractors can create projects" ON projects
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'contractor'
            AND deleted_at IS NULL
        )
    );

-- Policy for updating projects (project members with admin role or system admins)
CREATE POLICY "Users can update projects they have access to" ON projects
    FOR UPDATE
    USING (
        -- System admins can update any project
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'admin'
            AND deleted_at IS NULL
        )
        OR
        -- Project admins can update their projects
        EXISTS (
            SELECT 1 FROM project_users pu
            WHERE pu.user_id = auth.uid()
            AND pu.project_id = id
            AND pu.role = 'admin'
            AND pu.is_active = true
            AND pu.status = 'accepted'
            AND pu.deleted_at IS NULL
        )
    );

-- Create RLS policies for project_users table
ALTER TABLE project_users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view project members for accessible projects" ON project_users;
DROP POLICY IF EXISTS "Project admins can manage project members" ON project_users;

-- Policy for viewing project members
CREATE POLICY "Users can view project members for accessible projects" ON project_users
    FOR SELECT
    USING (user_has_project_access(auth.uid(), project_id));

-- Policy for managing project members (only project admins and system admins)
CREATE POLICY "Project admins can manage project members" ON project_users
    FOR ALL
    USING (
        -- System admins can manage any project members
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'admin'
            AND deleted_at IS NULL
        )
        OR
        -- Project admins can manage members of their projects
        EXISTS (
            SELECT 1 FROM project_users pu
            WHERE pu.user_id = auth.uid()
            AND pu.project_id = project_id
            AND pu.role = 'admin'
            AND pu.is_active = true
            AND pu.status = 'accepted'
            AND pu.deleted_at IS NULL
        )
    );

-- Create index for performance (avoid conflict with existing index)
CREATE INDEX IF NOT EXISTS idx_project_users_access_lookup_v2 
ON project_users (user_id, project_id, is_active, status, deleted_at);

-- ================================
-- MIGRATION NOTES
-- ================================
-- This migration implements project-based access control where:
-- 1. New users joining a company do NOT automatically see existing projects
-- 2. Users only see projects they created or were explicitly invited to
-- 3. Company membership does NOT automatically grant access to all company projects
-- 4. The project_users table becomes the primary access control mechanism
-- 5. RLS policies enforce access control at the database level
-- ================================