import { accessControlService } from '@/features/billing/services/access-control.service';
import { subscriptionsService } from '@/features/billing/services/subscriptions.service';
import { hasPermission } from '@/lib/rbac';
import { getUser } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const updateSubscriptionSchema = z.object({
  status: z
    .enum([
      'active',
      'pending_payment',
      'grace_period',
      'suspended',
      'cancelled',
    ])
    .optional(),
  gracePeriodEnds: z.string().datetime().optional(),
  nextBillingDate: z.string().datetime().optional(),
  failureReason: z.string().optional(),
});

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;

    // Get subscription with access information
    const result =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    if (!result.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = result.data;

    // Validate contractor access for non-admin users
    if (
      user.user_role === 'contractor' &&
      subscription.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot access other contractor subscriptions' },
        { status: 403 },
      );
    }

    // Get additional project access state information
    const accessState = await accessControlService.getProjectAccessState(
      subscription.project_id,
    );

    return NextResponse.json({
      data: {
        ...subscription,
        projectAccess: accessState.data,
      },
    });
  } catch (error) {
    console.error('GET /api/billing/subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to modify subscriptions' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;
    const body = await request.json();
    const validatedData = updateSubscriptionSchema.parse(body);

    // Get current subscription to validate ownership
    const currentSubscription =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (!currentSubscription.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    // Contractors can only modify their own subscriptions
    if (
      user.user_role === 'contractor' &&
      currentSubscription.data.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot modify other contractor subscriptions' },
        { status: 403 },
      );
    }

    // Update subscription access with status changes
    if (validatedData.status) {
      const updateResult = await subscriptionsService.updateSubscriptionAccess({
        subscriptionId,
        status: validatedData.status,
        gracePeriodEnds: validatedData.gracePeriodEnds
          ? new Date(validatedData.gracePeriodEnds)
          : undefined,
        failureReason: validatedData.failureReason,
      });

      if (updateResult.error) {
        return NextResponse.json(
          { error: updateResult.error },
          { status: 400 },
        );
      }
    }

    // Update next billing date if provided
    if (validatedData.nextBillingDate) {
      const billingResult = await subscriptionsService.updateNextBillingDate(
        subscriptionId,
        new Date(validatedData.nextBillingDate),
      );

      if (billingResult.error) {
        return NextResponse.json(
          { error: billingResult.error },
          { status: 400 },
        );
      }
    }

    // Get updated subscription with access information
    const updatedSubscription =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (updatedSubscription.error || !updatedSubscription.data) {
      return NextResponse.json(
        { error: 'Failed to retrieve updated subscription' },
        { status: 500 },
      );
    }

    // Log access control changes for audit trail
    const accessChange =
      validatedData.status !== currentSubscription.data.status;
    if (accessChange) {
      console.log(
        `Subscription ${subscriptionId} access changed from ${currentSubscription.data.status} to ${updatedSubscription.data.status} by user ${user.id}`,
      );
    }

    return NextResponse.json({
      data: updatedSubscription.data,
      message: 'Subscription updated successfully',
      accessChanged: accessChange,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('PATCH /api/billing/subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can delete subscriptions
    if (user.user_role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can delete subscriptions' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;

    // Get subscription details before deletion for logging
    const currentSubscription =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (!currentSubscription.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    // Log the deletion for audit trail
    console.log(
      `Admin ${user.id} deleting subscription ${subscriptionId} for project ${currentSubscription.data.project_id}`,
    );

    // Instead of hard deletion, we'll cancel the subscription
    const cancelResult = await subscriptionsService.updateSubscriptionAccess({
      subscriptionId,
      status: 'cancelled',
      failureReason: `Cancelled by admin ${user.id}`,
    });

    if (cancelResult.error) {
      return NextResponse.json({ error: cancelResult.error }, { status: 400 });
    }

    // Optionally perform hard deletion if really needed
    const { searchParams } = new URL(request.url);
    const hardDelete = searchParams.get('hard') === 'true';

    if (hardDelete) {
      const deleteResult =
        await subscriptionsService.deleteSubscription(subscriptionId);

      if (!deleteResult.success) {
        return NextResponse.json(
          { error: deleteResult.error },
          { status: 500 },
        );
      }

      return NextResponse.json({
        message: 'Subscription permanently deleted',
        projectId: currentSubscription.data.project_id,
        accessRevoked: true,
      });
    }

    return NextResponse.json({
      message: 'Subscription cancelled successfully',
      data: cancelResult.data,
      accessRevoked: true,
    });
  } catch (error) {
    console.error('DELETE /api/billing/subscriptions/[id] error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
