import { accessControlService } from '@/features/billing/services/access-control.service';
import { subscriptionsService } from '@/features/billing/services/subscriptions.service';
import { hasPermission } from '@/lib/rbac';
import { getUser } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const updateAccessSchema = z.object({
  action: z.enum(['restore', 'revoke', 'update']),
  reason: z.string().optional(),
  gracePeriodDays: z.number().min(1).max(30).optional(),
});

const bulkAccessCheckSchema = z.object({
  projectIds: z.array(z.string().uuid()),
});

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;

    // Get subscription with access information
    const subscriptionResult =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (subscriptionResult.error) {
      return NextResponse.json(
        { error: subscriptionResult.error },
        { status: 500 },
      );
    }

    if (!subscriptionResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // Validate contractor access for non-admin users
    if (
      user.user_role === 'contractor' &&
      subscription.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot access other contractor subscriptions' },
        { status: 403 },
      );
    }

    // Check detailed project access
    const accessCheck = await accessControlService.checkProjectAccess(
      subscription.contractor_id,
      subscription.project_id,
      'contractor',
    );

    // Get project access state
    const accessState = await accessControlService.getProjectAccessState(
      subscription.project_id,
    );

    return NextResponse.json({
      data: {
        subscriptionId: subscription.id,
        projectId: subscription.project_id,
        contractorId: subscription.contractor_id,
        accessStatus: {
          hasAccess: accessCheck.hasAccess,
          reason: accessCheck.reason,
          message: accessCheck.message,
          gracePeriodDays: accessCheck.gracePeriodDays,
        },
        subscription: {
          status: subscription.status,
          accessAllowed: subscription.accessAllowed,
          isInGracePeriod: subscription.isInGracePeriod,
          daysUntilSuspension: subscription.daysUntilSuspension,
          nextBillingDate: subscription.next_billing_date,
          gracePeriodEnds: subscription.grace_period_ends,
        },
        projectAccessState: accessState.data,
        lastChecked: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('GET /api/billing/subscriptions/[id]/access error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to restore access' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;
    const body = await request.json();
    const validatedData = updateAccessSchema.parse(body);

    // Get current subscription
    const subscriptionResult =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

    if (!subscriptionResult.data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // Validate contractor access for non-admin users
    if (
      user.user_role === 'contractor' &&
      subscription.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot modify other contractor subscriptions' },
        { status: 403 },
      );
    }

    let result;
    const reason =
      validatedData.reason ||
      `Access ${validatedData.action} by ${user.user_role} ${user.id}`;

    switch (validatedData.action) {
      case 'restore':
        result =
          await accessControlService.restoreProjectAccess(subscriptionId);
        break;

      case 'revoke':
        result = await accessControlService.revokeProjectAccess(
          subscriptionId,
          reason,
        );
        break;

      case 'update':
        // Update access permissions (could extend grace period, etc.)
        if (validatedData.gracePeriodDays) {
          const gracePeriodEnd = new Date();
          gracePeriodEnd.setDate(
            gracePeriodEnd.getDate() + validatedData.gracePeriodDays,
          );

          const updateResult =
            await subscriptionsService.updateSubscriptionAccess({
              subscriptionId,
              status: 'grace_period',
              gracePeriodEnds: gracePeriodEnd,
              failureReason: reason,
            });

          result = { success: !updateResult.error, error: updateResult.error };
        } else {
          return NextResponse.json(
            { error: 'Update action requires additional parameters' },
            { status: 400 },
          );
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Log access control action for audit trail
    console.log(
      `Access ${validatedData.action} for subscription ${subscriptionId} by user ${user.id}: ${reason}`,
    );

    // Get updated access status
    const updatedSubscription =
      await subscriptionsService.getSubscriptionWithAccess(subscriptionId);
    const accessCheck = await accessControlService.checkProjectAccess(
      subscription.contractor_id,
      subscription.project_id,
      'contractor',
    );

    return NextResponse.json({
      data: {
        action: validatedData.action,
        success: true,
        reason,
        updatedAccess: {
          hasAccess: accessCheck.hasAccess,
          reason: accessCheck.reason,
          message: accessCheck.message,
        },
        subscription: updatedSubscription.data,
        timestamp: new Date().toISOString(),
      },
      message: `Access ${validatedData.action} completed successfully`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('POST /api/billing/subscriptions/[id]/access error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.edit')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update access permissions' },
        { status: 403 },
      );
    }

    const { id: subscriptionId } = await params;
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'bulk_check';

    if (action === 'bulk_check') {
      const validatedData = bulkAccessCheckSchema.parse(body);

      // Get subscription to get contractor ID
      const subscriptionResult =
        await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

      if (!subscriptionResult.data) {
        return NextResponse.json(
          { error: 'Subscription not found' },
          { status: 404 },
        );
      }

      const subscription = subscriptionResult.data;

      // Validate contractor access for non-admin users
      if (
        user.user_role === 'contractor' &&
        subscription.contractor_id !== user.id
      ) {
        return NextResponse.json(
          { error: 'Cannot check access for other contractors' },
          { status: 403 },
        );
      }

      // Perform bulk access check
      const bulkCheckResult = await accessControlService.bulkCheckProjectAccess(
        {
          userId: subscription.contractor_id,
          userRole: 'contractor',
          projectIds: validatedData.projectIds,
        },
      );

      if (bulkCheckResult.error) {
        return NextResponse.json(
          { error: bulkCheckResult.error },
          { status: 500 },
        );
      }

      // Convert Map to object for JSON response
      const accessResults: Record<
        string,
        {
          hasAccess: boolean;
          reason: string;
          message: string;
          subscriptionStatus?: string;
          accessState?: string;
        }
      > = {};
      bulkCheckResult.accessResults.forEach((result, projectId) => {
        accessResults[projectId] = result;
      });

      return NextResponse.json({
        data: {
          contractorId: subscription.contractor_id,
          subscriptionId,
          projectAccess: accessResults,
          checkedAt: new Date().toISOString(),
        },
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('PUT /api/billing/subscriptions/[id]/access error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
