'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import type { PaymentRecord, SubscriptionStatus } from '@/types/billing';
import { convertCentsToMyr } from '@/types/billing';
import {
  AlertTriangle,
  ArrowDownRight,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  CreditCard,
  DollarSign,
  Download,
  ExternalLink,
  FileText,
  Receipt,
  Search,
  TrendingUp,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface PaymentHistoryItem extends PaymentRecord {
  projectName: string;
  projectId: string;
  accessImpact?: {
    beforeStatus: SubscriptionStatus;
    afterStatus: SubscriptionStatus;
    restored: boolean;
  };
  receiptAvailable?: boolean;
}

export interface PaymentHistoryProps {
  payments: PaymentHistoryItem[];
  loading?: boolean;
  error?: string;
  onLoadMore?: () => void;
  onDownloadReceipt?: (paymentId: string) => void;
  onViewDetails?: (paymentId: string) => void;
  className?: string;
  language?: 'en' | 'ms';
  showFilters?: boolean;
  showStats?: boolean;
}

type PaymentStatus = 'all' | 'paid' | 'pending' | 'failed' | 'refunded';
type SortField = 'date' | 'amount' | 'project' | 'status';
type SortOrder = 'asc' | 'desc';

/**
 * Comprehensive payment history with access impact indicators and filtering
 */
export function PaymentHistory({
  payments,
  loading = false,
  error,
  onLoadMore,
  onDownloadReceipt,
  onViewDetails,
  className,
  language = 'en',
  showFilters = true,
  showStats = true,
}: PaymentHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<PaymentStatus>('all');
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [expandedPayment, setExpandedPayment] = useState<string | null>(null);
  const [showReceiptGenerator, setShowReceiptGenerator] = useState<
    string | null
  >(null);

  // Translations
  const translations = {
    en: {
      title: 'Payment History',
      subtitle: 'View and manage your payment records',
      search: 'Search payments...',
      status: 'Status',
      sortBy: 'Sort by',
      allPayments: 'All Payments',
      paid: 'Paid',
      pending: 'Pending',
      failed: 'Failed',
      refunded: 'Refunded',
      date: 'Date',
      amount: 'Amount',
      project: 'Project',
      sortDate: 'Date',
      sortAmount: 'Amount',
      sortProject: 'Project',
      sortStatus: 'Status',
      totalSpent: 'Total Spent',
      successfulPayments: 'Successful Payments',
      averagePayment: 'Average Payment',
      lastPayment: 'Last Payment',
      accessRestored: 'Access Restored',
      accessMaintained: 'Access Maintained',
      noImpact: 'No Impact',
      viewReceipt: 'View Receipt',
      downloadReceipt: 'Download Receipt',
      viewDetails: 'View Details',
      paymentMethod: 'Payment Method',
      transactionId: 'Transaction ID',
      accessImpact: 'Access Impact',
      before: 'Before',
      after: 'After',
      noPayments: 'No payments found',
      noPaymentsDesc: 'When you make payments, they will appear here.',
      filterNoResults: 'No payments match your filters',
      filterNoResultsDesc: 'Try adjusting your search or filter criteria.',
      loadMore: 'Load More Payments',
      error: 'Error loading payments',
    },
    ms: {
      title: 'Sejarah Pembayaran',
      subtitle: 'Lihat dan urus rekod pembayaran anda',
      search: 'Cari pembayaran...',
      status: 'Status',
      sortBy: 'Isih mengikut',
      allPayments: 'Semua Pembayaran',
      paid: 'Dibayar',
      pending: 'Belum Selesai',
      failed: 'Gagal',
      refunded: 'Dipulangkan',
      date: 'Tarikh',
      amount: 'Jumlah',
      project: 'Projek',
      sortDate: 'Tarikh',
      sortAmount: 'Jumlah',
      sortProject: 'Projek',
      sortStatus: 'Status',
      totalSpent: 'Jumlah Belanja',
      successfulPayments: 'Pembayaran Berjaya',
      averagePayment: 'Purata Pembayaran',
      lastPayment: 'Pembayaran Terakhir',
      accessRestored: 'Akses Dipulihkan',
      accessMaintained: 'Akses Dikekalkan',
      noImpact: 'Tiada Kesan',
      viewReceipt: 'Lihat Resit',
      downloadReceipt: 'Muat Turun Resit',
      viewDetails: 'Lihat Butiran',
      paymentMethod: 'Kaedah Pembayaran',
      transactionId: 'ID Transaksi',
      accessImpact: 'Kesan Akses',
      before: 'Sebelum',
      after: 'Selepas',
      noPayments: 'Tiada pembayaran dijumpai',
      noPaymentsDesc:
        'Apabila anda membuat pembayaran, ia akan muncul di sini.',
      filterNoResults: 'Tiada pembayaran sepadan dengan penapis',
      filterNoResultsDesc: 'Cuba laraskan kriteria carian atau penapis anda.',
      loadMore: 'Muatkan Lebih Banyak Pembayaran',
      error: 'Ralat memuatkan pembayaran',
    },
  };

  const t = translations[language];

  // Computed statistics
  const stats = useMemo(() => {
    const paidPayments = payments.filter((p) => p.status === 'paid');
    const totalSpent = paidPayments.reduce(
      (sum, p) => sum + (p.amount || 0),
      0,
    );
    const averagePayment =
      paidPayments.length > 0 ? totalSpent / paidPayments.length : 0;
    const lastPayment = paidPayments.sort(
      (a, b) =>
        new Date(b.created_at || '').getTime() -
        new Date(a.created_at || '').getTime(),
    )[0];

    return {
      totalSpent: convertCentsToMyr(totalSpent),
      successfulPayments: paidPayments.length,
      averagePayment: convertCentsToMyr(averagePayment),
      lastPaymentDate: lastPayment?.created_at,
    };
  }, [payments]);

  // Filtered and sorted payments
  const filteredPayments = useMemo(() => {
    const filtered = payments.filter((payment) => {
      const matchesSearch =
        searchTerm === '' ||
        payment.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.id.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus =
        statusFilter === 'all' || payment.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    // Sort payments
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortField) {
        case 'date':
          comparison =
            new Date(a.created_at || '').getTime() -
            new Date(b.created_at || '').getTime();
          break;
        case 'amount':
          comparison = (a.amount || 0) - (b.amount || 0);
          break;
        case 'project':
          comparison = a.projectName.localeCompare(b.projectName);
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [payments, searchTerm, statusFilter, sortField, sortOrder]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusVariant = (
    status: string,
  ): 'default' | 'secondary' | 'destructive' => {
    switch (status) {
      case 'paid':
        return 'default';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getAccessImpactIndicator = (
    impact?: PaymentHistoryItem['accessImpact'],
  ) => {
    if (!impact) return null;

    if (impact.restored) {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <ArrowUpRight className="h-4 w-4" />
          <span className="text-sm font-medium">{t.accessRestored}</span>
        </div>
      );
    }

    if (impact.beforeStatus !== impact.afterStatus) {
      return (
        <div className="flex items-center gap-1 text-blue-600">
          <TrendingUp className="h-4 w-4" />
          <span className="text-sm font-medium">{t.accessMaintained}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 text-gray-500">
        <span className="text-sm">{t.noImpact}</span>
      </div>
    );
  };

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {t.error}: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">{t.title}</h2>
        <p className="text-muted-foreground">{t.subtitle}</p>
      </div>

      {/* Statistics */}
      {showStats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">{t.totalSpent}</span>
              </div>
              <p className="text-2xl font-bold">
                RM {stats.totalSpent.toFixed(2)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">
                  {t.successfulPayments}
                </span>
              </div>
              <p className="text-2xl font-bold">{stats.successfulPayments}</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">{t.averagePayment}</span>
              </div>
              <p className="text-2xl font-bold">
                RM {stats.averagePayment.toFixed(2)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">{t.lastPayment}</span>
              </div>
              <p className="text-sm font-semibold">
                {stats.lastPaymentDate
                  ? new Date(stats.lastPaymentDate).toLocaleDateString()
                  : '-'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select
                value={statusFilter}
                onValueChange={(value) =>
                  setStatusFilter(value as PaymentStatus)
                }
              >
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder={t.status} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t.allPayments}</SelectItem>
                  <SelectItem value="paid">{t.paid}</SelectItem>
                  <SelectItem value="pending">{t.pending}</SelectItem>
                  <SelectItem value="failed">{t.failed}</SelectItem>
                  <SelectItem value="refunded">{t.refunded}</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={`${sortField}-${sortOrder}`}
                onValueChange={(value) => {
                  const [field, order] = value.split('-') as [
                    SortField,
                    SortOrder,
                  ];
                  setSortField(field);
                  setSortOrder(order);
                }}
              >
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder={t.sortBy} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date-desc">
                    {t.sortDate} (Newest)
                  </SelectItem>
                  <SelectItem value="date-asc">
                    {t.sortDate} (Oldest)
                  </SelectItem>
                  <SelectItem value="amount-desc">
                    {t.sortAmount} (High)
                  </SelectItem>
                  <SelectItem value="amount-asc">
                    {t.sortAmount} (Low)
                  </SelectItem>
                  <SelectItem value="project-asc">
                    {t.sortProject} (A-Z)
                  </SelectItem>
                  <SelectItem value="project-desc">
                    {t.sortProject} (Z-A)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment List */}
      <div className="space-y-4">
        {loading ? (
          // Loading skeletons
          Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-1/3" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-1/4" />
                  </div>
                  <div className="space-y-2 text-right">
                    <Skeleton className="h-6 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredPayments.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Receipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {payments.length === 0 ? t.noPayments : t.filterNoResults}
              </h3>
              <p className="text-muted-foreground">
                {payments.length === 0
                  ? t.noPaymentsDesc
                  : t.filterNoResultsDesc}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredPayments.map((payment) => (
            <Card key={payment.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-3">
                      <h3 className="font-semibold">{payment.projectName}</h3>
                      <Badge variant={getStatusVariant(payment.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(payment.status)}
                          {payment.status}
                        </div>
                      </Badge>
                    </div>

                    <p className="text-sm text-muted-foreground">
                      {new Date(payment.created_at || '').toLocaleDateString()}{' '}
                      • ID: {payment.id.slice(-8).toUpperCase()}
                    </p>

                    {payment.accessImpact && (
                      <div className="pt-1">
                        {getAccessImpactIndicator(payment.accessImpact)}
                      </div>
                    )}
                  </div>

                  <div className="text-right space-y-2">
                    <p className="text-2xl font-bold">
                      RM {convertCentsToMyr(payment.amount || 0).toFixed(2)}
                    </p>

                    <div className="flex gap-2">
                      {payment.receiptAvailable && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDownloadReceipt?.(payment.id)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          {t.downloadReceipt}
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          setExpandedPayment(
                            expandedPayment === payment.id ? null : payment.id,
                          )
                        }
                      >
                        {expandedPayment === payment.id ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {expandedPayment === payment.id && (
                  <>
                    <Separator className="my-4" />
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <h4 className="font-medium">{t.paymentMethod}</h4>
                        <p className="text-sm text-muted-foreground flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          BillPlz Gateway
                        </p>

                        {payment.billplz_bill_id && (
                          <div>
                            <h4 className="font-medium mb-1">
                              {t.transactionId}
                            </h4>
                            <p className="text-sm font-mono text-muted-foreground">
                              {payment.billplz_bill_id}
                            </p>
                          </div>
                        )}
                      </div>

                      {payment.accessImpact && (
                        <div className="space-y-3">
                          <h4 className="font-medium">{t.accessImpact}</h4>
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <p className="text-xs text-muted-foreground mb-1">
                                {t.before}
                              </p>
                              <ProjectAccessIndicator
                                status={payment.accessImpact.beforeStatus}
                                size="sm"
                              />
                            </div>
                            <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
                            <div className="text-center">
                              <p className="text-xs text-muted-foreground mb-1">
                                {t.after}
                              </p>
                              <ProjectAccessIndicator
                                status={payment.accessImpact.afterStatus}
                                size="sm"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowReceiptGenerator(payment.id)}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        {t.viewReceipt}
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewDetails?.(payment.id)}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        {t.viewDetails}
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Load More */}
      {onLoadMore && !loading && (
        <div className="text-center">
          <Button variant="outline" onClick={onLoadMore}>
            {t.loadMore}
          </Button>
        </div>
      )}

      {/* Receipt Generator Modal */}
      {showReceiptGenerator && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="font-semibold">{t.viewReceipt}</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReceiptGenerator(null)}
              >
                ×
              </Button>
            </div>
            <div className="p-4">
              {/* This would render the ReceiptGenerator component */}
              <div className="text-center py-8 text-muted-foreground">
                Receipt generator would be rendered here
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
