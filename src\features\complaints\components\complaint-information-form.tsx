'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useUserWithProfile } from '@/features/auth/hooks/use-auth';
import { usePMACertificates } from '@/features/pma-management/hooks/use-pma-certificates';
import { useProjectDetails } from '@/features/projects/hooks/use-project-details';
import { useContractorProfile } from '@/hooks/use-contractor-profile';
import { uploadToOBS } from '@/lib/obs-upload';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useCreateComplaint } from '../hooks/use-complaints-simple';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';

interface ComplaintInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function ComplaintInformationForm({
  onSuccess,
  onCancel,
}: ComplaintInformationFormProps) {
  const [beforePhotos, setBeforePhotos] = useState<File[]>([]);
  const [uploadedBeforeUrls, setUploadedBeforeUrls] = useState<string[]>([]);
  const [isUploadingBefore, setIsUploadingBefore] = useState(false);
  const [selectedPMAId, setSelectedPMAId] = useState<string>('');

  // Hooks for data
  const { data: userWithProfile } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();
  const { data: projectDetails } = useProjectDetails(selectedProjectId || '');
  const { data: pmaData } = usePMACertificates(selectedProjectId, 1, 100); // Get all PMAs for the project

  // Get contractor profile data if user is a contractor
  const isContractor = userWithProfile?.profile?.user_role === 'contractor';
  const { data: contractorProfile } = useContractorProfile(
    userWithProfile?.id,
    isContractor,
  );

  // Debug logging for agency data
  useEffect(() => {
    console.log('Debug - Project Details:', projectDetails);
    console.log('Debug - Agency Data:', projectDetails?.agency);
    console.log('Debug - Selected Project ID:', selectedProjectId);
  }, [projectDetails, selectedProjectId]);

  const createComplaintMutation = useCreateComplaint();
  const t = useTranslations('complaints.form');

  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: '',
      date: new Date(),
      expected_completion_date: new Date(),
      contractor_name: '',
      location: '',
      description: '',
      involves_mantrap: false,
      no_pma_lif: '',
      actual_completion_date: undefined,
      status: 'open',
      repair_cost: 0,
    },
  });

  // Auto-fill user and project data when available
  useEffect(() => {
    if (userWithProfile?.email) {
      form.setValue('email', userWithProfile.email);
    }

    // Use the user's name from the profile (users table)
    if (userWithProfile?.profile?.name) {
      form.setValue('contractor_name', userWithProfile.profile.name);
    }
  }, [userWithProfile, form]);

  // Handle PMA selection and auto-fill location
  const handlePMASelection = (pmaId: string) => {
    setSelectedPMAId(pmaId);
    const selectedPMA = pmaData?.data.find((pma) => pma.id === pmaId);
    if (selectedPMA) {
      form.setValue('no_pma_lif', selectedPMA.pma_number || '');
      if (selectedPMA.location) {
        form.setValue('location', selectedPMA.location);
      }
    }
  };

  const handleBeforePhotoUpload = async (files: File[]) => {
    setBeforePhotos(files);
    if (files.length > 0) {
      await uploadBeforeFiles(files);
    }
  };

  const uploadBeforeFiles = async (files: File[]) => {
    setIsUploadingBefore(true);
    const newUrls: string[] = [];

    try {
      for (const file of files) {
        const url = await uploadToOBS({
          file,
          folder: 'complaints/before-repair',
        });
        newUrls.push(url);
      }
      setUploadedBeforeUrls((prev) => [...prev, ...newUrls]);
    } catch (error) {
      console.error('Failed to upload before photos:', error);
    } finally {
      setIsUploadingBefore(false);
    }
  };

  const removeBeforePhoto = (index: number) => {
    setBeforePhotos((prev) => prev.filter((_, i) => i !== index));
    setUploadedBeforeUrls((prev) => prev.filter((_, i) => i !== index));
  };
  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      console.log('Form submission started');
      console.log('Form data:', data);
      console.log('Form errors:', form.formState.errors);
      console.log('Before photos:', beforePhotos);

      const result = await createComplaintMutation.mutateAsync({
        ...data,
        beforeRepairFiles: beforePhotos,
        beforeRepairUrls: uploadedBeforeUrls,
      });

      console.log('Mutation result:', result);

      // Show success message with ticket ID
      if (result.number) {
        console.log(
          `Complaint created successfully! Ticket ID: ${result.number}`,
        );
      }

      onSuccess();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* A. Complaint Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-blue-50">
            <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
              {t('sectionA.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.email')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  className={cn(
                    'h-10 bg-gray-50',
                    form.formState.errors.email &&
                      'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder={t('placeholders.email')}
                  readOnly
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contactNumber"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.name')}
                </Label>{' '}
                <Input
                  id="contactNumber"
                  {...form.register('contractor_name')}
                  className="h-10 bg-gray-50"
                  placeholder="Ahmad Zainuddin"
                  readOnly
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.complaintDate')}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full h-10 justify-start text-left font-normal',
                        !form.watch('date') && 'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.watch('date') ? (
                        format(form.watch('date'), 'yyyy-MM-dd')
                      ) : (
                        <span>{t('placeholders.selectDate')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={form.watch('date')}
                      onSelect={(date) =>
                        form.setValue('date', date || new Date())
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.agency')}
                </Label>{' '}
                <Input
                  value={
                    projectDetails?.agency?.name ||
                    (projectDetails ? 'No agency assigned' : 'Loading...')
                  }
                  className="h-10 bg-gray-50"
                  placeholder={t('placeholders.selectAgency')}
                  readOnly
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contractorCompanyName"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.contractorCompanyName')}
                </Label>{' '}
                <Input
                  id="contractorCompanyName"
                  value={contractorProfile?.contractor?.name || ''}
                  className="h-10 bg-gray-50"
                  placeholder="Syarikat Lif Teknologi Sdn Bhd"
                  readOnly
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="location"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.location')}
              </Label>
              <Input
                id="location"
                {...form.register('location')}
                className="h-10 bg-gray-50"
                placeholder=""
                readOnly
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="noPmaLif"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.pmaNumber')}
              </Label>{' '}
              <Select value={selectedPMAId} onValueChange={handlePMASelection}>
                <SelectTrigger className="h-10">
                  <SelectValue
                    placeholder={t('placeholders.selectPMA') || 'Select PMA'}
                  />
                </SelectTrigger>
                <SelectContent>
                  {pmaData?.data.map((pma) => (
                    <SelectItem key={pma.id} value={pma.id}>
                      {pma.pma_number || 'Unknown PMA Number'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.damageDescription')}
              </Label>
              <Input
                {...form.register('description')}
                className="h-10"
                placeholder=""
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.expectedCompletionDate')}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full h-10 justify-start text-left font-normal',
                      !form.watch('expected_completion_date') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('expected_completion_date') ? (
                      format(
                        form.watch('expected_completion_date'),
                        'yyyy-MM-dd',
                      )
                    ) : (
                      <span>{t('placeholders.selectDate')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={form.watch('expected_completion_date')}
                    onSelect={(date) =>
                      form.setValue(
                        'expected_completion_date',
                        date || new Date(),
                      )
                    }
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.involvesManTrap')}
                </Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="true"
                      checked={form.watch('involves_mantrap') === true}
                      onChange={() => form.setValue('involves_mantrap', true)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.yes')}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="false"
                      checked={form.watch('involves_mantrap') === false}
                      onChange={() => form.setValue('involves_mantrap', false)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.no')}</span>
                  </label>
                </div>
              </div>

              {/* Before Photo Upload */}
              <div className="mt-4 space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.beforePhoto')}
                </Label>
                <FileUploadDropzone
                  onFilesChange={handleBeforePhotoUpload}
                  files={beforePhotos}
                  accept=".jpg,.jpeg,.png,.pdf"
                  maxSize={10 * 1024 * 1024} // 10MB
                  maxFiles={5}
                  onFileRemove={removeBeforePhoto}
                  disabled={isUploadingBefore}
                  className="w-full"
                />

                {/* Display upload status */}
                {isUploadingBefore && (
                  <div className="text-sm text-blue-600 flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                    Uploading photos...
                  </div>
                )}

                {/* Display uploaded URLs */}
                {uploadedBeforeUrls.length > 0 && (
                  <div className="mt-2 space-y-1">
                    <p className="text-xs text-gray-600">
                      Successfully uploaded:
                    </p>
                    {uploadedBeforeUrls.map((url, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-green-50 rounded text-sm"
                      >
                        <span className="text-green-700">
                          {url.split('/').pop() || `Photo ${index + 1}`}
                        </span>
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-800 text-xs"
                        >
                          View
                        </a>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Note */}
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-orange-600 mt-1">⚠️</div>
              <div className="text-sm">
                <div className="font-medium text-orange-800 mb-1">
                  {t('notes.title')}
                </div>
                <ul className="space-y-1 text-orange-700">
                  <li>• {t('notes.required')}</li>
                  <li>• {t('notes.proofRequired')}</li>
                  <li>• {t('notes.autoSubmit')}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="w-full sm:w-auto px-6"
          >
            {t('buttons.back')}
          </Button>
          <Button
            type="submit"
            disabled={createComplaintMutation.isPending || isUploadingBefore}
            className="w-full sm:w-auto px-8 bg-blue-600 hover:bg-blue-700"
          >
            {createComplaintMutation.isPending ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {t('buttons.submitting')}
              </>
            ) : isUploadingBefore ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Uploading photos...
              </>
            ) : (
              <>{t('buttons.submit')}</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
