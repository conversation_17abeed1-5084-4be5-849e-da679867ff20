# Project Access Control Changes

## Overview

This document outlines the changes made to implement project-based access control, where users only see projects they specifically created or were explicitly invited to, rather than automatically seeing all projects from their company.

## Changes Made

### 1. Database Migration (`20250125000000_implement_project_based_access_control.sql`)

**Key Changes:**
- Added `status` column to `project_users` table (if not exists)
- Created automatic trigger to add project creators to `project_users` table
- Implemented Row Level Security (RLS) policies for `projects` and `project_users` tables
- Created `user_has_project_access()` function for centralized access control logic

**Access Control Logic:**
- **Admin users with project-level access**: Can see all projects
- **Admin users with state-level access**: Can only see projects in their monitoring state
- **Contractors and viewers**: Can only see projects they're members of via `project_users` table

### 2. Updated Project Access Hook (`use-project-access.ts`)

**Before:**
- Manual filtering based on `contractor_id` for company-wide access
- Complex OR conditions combining company access and project membership

**After:**
- Simplified to use RLS policies for access control
- Database handles all filtering automatically
- Cleaner, more secure implementation

### 3. Updated Access Control Types (`access-control.ts`)

**Changes:**
- Updated comments to reflect that RLS policies now handle primary access control
- Maintained backward compatibility for client-side checks
- Simplified filtering logic since database now handles most filtering

### 4. Updated Project Service (`project-service.ts`)

**Changes:**
- Removed manual insertion of project creator to `project_users` (now handled by trigger)
- Maintained JKR user invitation logic
- Cleaner project creation flow

### 5. Updated Invitation Service (`invitation-service.ts`)

**Changes:**
- Added explicit `status: 'accepted'` when adding users to projects
- Updated user-in-project checks to include `status` and `deleted_at` filters
- Ensures compatibility with new access control model

## Access Control Rules

### New User Behavior
- **Before**: New users joining a company automatically saw ALL existing company projects
- **After**: New users see NO projects until explicitly invited

### Project Visibility
- **Before**: Company membership = access to all company projects
- **After**: Project access = explicit membership in `project_users` table

### User Roles
1. **Contractors**: 
   - Can create projects (automatically become project admin)
   - Can only see projects they created or were invited to
   
2. **Admin (Project-level)**:
   - Cannot create projects
   - Can see all projects across all companies
   - Can be invited to specific projects
   
3. **Admin (State-level)**:
   - Cannot create projects
   - Can see all projects in their assigned state
   - Can be invited to specific projects
   
4. **Viewers**:
   - Cannot create projects
   - Can only see projects they were invited to

## Database Schema Changes

### New Columns
- `project_users.status` - Tracks invitation status ('accepted', 'pending', etc.)

### New Functions
- `add_project_creator_to_project_users()` - Automatically adds project creators
- `user_has_project_access()` - Centralized access control logic

### New Policies
- RLS policies on `projects` table for viewing, creating, and updating
- RLS policies on `project_users` table for viewing and managing members

## Migration Impact

### Existing Data
- Existing projects remain unchanged
- Existing `project_users` entries get default `status: 'accepted'`
- No data loss or breaking changes

### User Experience
- Users may see fewer projects initially (only those they have explicit access to)
- Project creators maintain full access to their projects
- Invitation system becomes the primary way to grant project access

## Security Benefits

1. **Principle of Least Privilege**: Users only see what they need
2. **Database-Level Enforcement**: RLS policies prevent unauthorized access
3. **Audit Trail**: All access is tracked through `project_users` table
4. **Scalable**: Works efficiently with large numbers of projects and users

## Testing Recommendations

1. **Test project creation** - Ensure creators get automatic access
2. **Test invitations** - Verify invited users can access projects
3. **Test role-based access** - Confirm admin users see appropriate projects
4. **Test RLS policies** - Verify unauthorized access is blocked
5. **Test existing data** - Ensure no disruption to current users

## Rollback Plan

If issues arise, the migration can be rolled back by:
1. Dropping the RLS policies
2. Removing the trigger
3. Reverting the application code changes
4. Using the previous company-based access control logic