-- ================================
-- MIGRATION: PROJECT-BASED TO PMA-BASED BILLING
-- Phase 1: Create PMA Subscriptions Table and Migrate Data
-- ================================

-- ================================
-- PMA SUBSCRIPTIONS TABLE
-- Core subscription management per PMA certificate
-- ================================

CREATE TABLE public.pma_subscriptions (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    pma_certificate_id uuid NOT NULL,
    contractor_id uuid NOT NULL,
    amount decimal(10,2) DEFAULT 150.00 NOT NULL,
    currency text DEFAULT 'MYR' NOT NULL,
    status public.subscription_status DEFAULT 'pending_payment' NOT NULL,
    billing_cycle text DEFAULT 'monthly' NOT NULL,
    next_billing_date timestamp with time zone,
    grace_period_ends timestamp with time zone,
    access_allowed boolean GENERATED ALWAYS AS (
        status IN ('active', 'grace_period')
    ) STORED,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_pma_subscriptions_pma_certificate_id 
        FOREIGN KEY (pma_certificate_id) REFERENCES public.pma_certificates(id) ON DELETE CASCADE,
    CONSTRAINT fk_pma_subscriptions_contractor_id 
        FOREIGN KEY (contractor_id) REFERENCES public.contractors(id) ON DELETE CASCADE,
        
    -- Unique constraint: one subscription per PMA certificate
    CONSTRAINT uq_pma_subscriptions_pma_certificate_id UNIQUE (pma_certificate_id),
    
    -- Business logic constraints
    CONSTRAINT chk_pma_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_pma_currency_valid CHECK (currency IN ('MYR')),
    CONSTRAINT chk_pma_billing_cycle_valid CHECK (billing_cycle IN ('monthly')),
    CONSTRAINT chk_pma_grace_period_logic CHECK (
        (status = 'grace_period' AND grace_period_ends IS NOT NULL) OR
        (status != 'grace_period' AND grace_period_ends IS NULL)
    )
);

-- ================================
-- NEW PAYMENT RECORDS TABLE FOR PMA SUBSCRIPTIONS
-- ================================

CREATE TABLE public.pma_payment_records (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    pma_subscription_id uuid NOT NULL,
    billplz_bill_id text UNIQUE,
    amount decimal(10,2) NOT NULL,
    status public.payment_status DEFAULT 'pending' NOT NULL,
    paid_at timestamp with time zone,
    failure_reason text,
    billplz_response jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_pma_payment_records_subscription_id 
        FOREIGN KEY (pma_subscription_id) REFERENCES public.pma_subscriptions(id) ON DELETE CASCADE,
        
    -- Business logic constraints
    CONSTRAINT chk_pma_payment_amount_positive CHECK (amount > 0),
    CONSTRAINT chk_pma_paid_at_logic CHECK (
        (status = 'paid' AND paid_at IS NOT NULL) OR
        (status != 'paid' AND paid_at IS NULL)
    )
);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- PMA subscriptions indexes
CREATE INDEX idx_pma_subscriptions_contractor_status 
    ON public.pma_subscriptions(contractor_id, status);
CREATE INDEX idx_pma_subscriptions_next_billing 
    ON public.pma_subscriptions(next_billing_date) 
    WHERE status = 'active';
CREATE INDEX idx_pma_subscriptions_grace_period 
    ON public.pma_subscriptions(grace_period_ends) 
    WHERE status = 'grace_period';

-- Computed field index for fast access control queries
CREATE INDEX idx_pma_subscriptions_access_allowed 
    ON public.pma_subscriptions(access_allowed, contractor_id)
    WHERE access_allowed = true;

-- PMA payment records indexes  
CREATE INDEX idx_pma_payment_records_subscription_status 
    ON public.pma_payment_records(pma_subscription_id, status);
CREATE INDEX idx_pma_payment_records_billplz_id 
    ON public.pma_payment_records(billplz_bill_id) 
    WHERE billplz_bill_id IS NOT NULL;
CREATE INDEX idx_pma_payment_records_created_at 
    ON public.pma_payment_records(created_at DESC);

-- ================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ================================

-- Trigger for pma_subscriptions
CREATE TRIGGER trigger_update_pma_subscriptions_updated_at 
    BEFORE UPDATE ON public.pma_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ================================
-- NO DATA MIGRATION REQUIRED
-- Starting fresh with PMA-based billing system
-- ================================

-- ================================
-- COMMENTS FOR DOCUMENTATION
-- ================================

COMMENT ON TABLE public.pma_subscriptions IS 'Monthly subscription management per PMA certificate with access control';
COMMENT ON TABLE public.pma_payment_records IS 'BillPlz payment transaction history for PMA subscriptions';

COMMENT ON COLUMN public.pma_subscriptions.status IS 'Subscription status controlling PMA certificate access';
COMMENT ON COLUMN public.pma_subscriptions.access_allowed IS 'Computed field: true when status allows PMA access (active or grace_period)';
COMMENT ON COLUMN public.pma_subscriptions.grace_period_ends IS 'End of 7-day grace period after payment failure';
COMMENT ON COLUMN public.pma_payment_records.billplz_response IS 'Complete BillPlz API response for debugging';

-- ================================
-- READY FOR PMA-BASED BILLING
-- ================================

-- ================================
-- END OF MIGRATION  
-- ================================