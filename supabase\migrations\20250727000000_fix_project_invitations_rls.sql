-- ================================
-- FIX PROJECT INVITATIONS RLS POLICIES
-- ================================

-- Enable RLS on project_invitations table
ALTER TABLE project_invitations ENABLE ROW LEVEL SECURITY;

-- Allow users to view invitations for projects they have access to
CREATE POLICY "Enable read access for project invitations" ON project_invitations
    FOR SELECT
    TO authenticated
    USING (user_has_project_access(auth.uid(), project_id));

-- Allow project admins and system admins to manage invitations
CREATE POLICY "Enable all operations for project admins on invitations" ON project_invitations
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'admin'
            AND deleted_at IS NULL
        )
        OR
        EXISTS (
            SELECT 1 FROM project_users pu
            WHERE pu.user_id = auth.uid()
            AND pu.project_id = project_id
            AND pu.role = 'admin'
            AND pu.is_active = true
            AND pu.status = 'accepted'
            AND pu.deleted_at IS NULL
        )
    );