import { supabase } from '@/lib/supabase';
import type { Project, Database } from '@/types';
import type {
  PaymentStatus,
  ProjectSubscription,
  SubscriptionStatus,
} from '@/types/billing';
import { calculateNextBillingDate } from '@/types/billing';
import { paymentsService } from './payments.service';
import type { PmaSubscriptionWithDetails } from './pma-subscriptions.service';
import { pmaSubscriptionsService } from './pma-subscriptions.service';
import { subscriptionsService } from './subscriptions.service';

export interface ProjectWithBilling {
  id: string;
  name: string;
  contractor_id: string | null;
  created_at: string | null;
  pmaSubscriptions?: PmaSubscriptionWithDetails[];
  billingStatus: 'active' | 'pending' | 'none';
  needsBillingSetup: boolean;
  totalPmaCount: number;
  activePmaSubscriptions: number;
}

export interface IntegrateProjectBillingParams {
  projectId: string;
  contractorId: string;
  autoActivate?: boolean;
  gracePeriodDays?: number;
}

export interface MigrationResult {
  totalProjects: number;
  successfulMigrations: number;
  failedMigrations: number;
  errors: string[];
  projectResults: {
    projectId: string;
    projectName: string;
    success: boolean;
    error?: string;
  }[];
}

export class ProjectIntegrationService {
  /**
   * Get project basic info by ID
   */
  async getProject(projectId: string): Promise<{
    data: { id: string; name: string } | null;
    error: string | null;
  }> {
    try {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id, name')
        .eq('id', projectId)
        .single();

      if (projectError) {
        return {
          data: null,
          error: projectError.message,
        };
      }

      return {
        data: project,
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async integrateProjectBilling(
    params: IntegrateProjectBillingParams,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      const {
        projectId,
        contractorId,
        autoActivate = false,
        gracePeriodDays: _gracePeriodDays,
      } = params;

      // Verify project exists and belongs to contractor
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id, name, contractor_id')
        .eq('id', projectId)
        .single();

      if (projectError || !project) {
        return {
          data: null,
          error: projectError?.message || 'Project not found',
        };
      }

      if (project.contractor_id !== contractorId || !project.contractor_id) {
        return {
          data: null,
          error: 'Project does not belong to the specified contractor',
        };
      }

      // Check if subscription already exists
      const existingSubscription =
        await subscriptionsService.getByProjectId(projectId);
      if (existingSubscription.data) {
        return {
          data: null,
          error: 'Billing integration already exists for this project',
        };
      }

      // Determine initial subscription status
      const initialStatus: SubscriptionStatus = autoActivate
        ? 'active'
        : 'pending_payment';

      // Calculate next billing date
      const nextBillingDate = calculateNextBillingDate();

      // Use the initial status determined above
      const status = initialStatus;

      // Create subscription
      const subscriptionResult =
        await subscriptionsService.createSubscriptionForProject({
          projectId,
          contractorId,
          status,
          nextBillingDate,
        });

      if (subscriptionResult.error) {
        return {
          data: null,
          error: `Failed to create subscription: ${subscriptionResult.error}`,
        };
      }

      return { data: subscriptionResult.data, error: null };
    } catch (error) {
      console.error('Integrate project billing error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async migrateExistingProjects(
    contractorId?: string,
  ): Promise<MigrationResult> {
    const result: MigrationResult = {
      totalProjects: 0,
      successfulMigrations: 0,
      failedMigrations: 0,
      errors: [],
      projectResults: [],
    };

    try {
      // Get all projects without subscriptions
      let query = supabase.from('projects').select(`
          id,
          name,
          contractor_id,
          created_at
        `);

      // Filter by contractor if specified
      if (contractorId) {
        query = query.eq('contractor_id', contractorId);
      }

      const { data: projects, error: projectsError } = await query;

      if (projectsError) {
        result.errors.push(
          `Failed to fetch projects: ${projectsError.message}`,
        );
        return result;
      }

      if (!projects || projects.length === 0) {
        return result;
      }

      result.totalProjects = projects.length;

      // Get existing subscriptions to avoid duplicates
      const { data: existingSubscriptions } = await supabase
        .from('project_subscriptions')
        .select('project_id');

      const existingProjectIds = new Set(
        existingSubscriptions?.map((sub) => sub.project_id) || [],
      );

      // Filter out projects that already have subscriptions
      const projectsNeedingBilling = projects.filter(
        (project) => !existingProjectIds.has(project.id),
      );

      // Process each project
      for (const project of projectsNeedingBilling) {
        try {
          // Only migrate projects that have a contractor_id
          if (!project.contractor_id) {
            result.failedMigrations++;
            result.errors.push(
              `Project ${project.name}: No contractor assigned`,
            );
            result.projectResults.push({
              projectId: project.id,
              projectName: project.name,
              success: false,
              error: 'No contractor assigned',
            });
            continue;
          }

          // Grandfather existing projects with active status
          const migrationResult = await this.integrateProjectBilling({
            projectId: project.id,
            contractorId: project.contractor_id,
            autoActivate: true, // Grandfather with active status
          });

          if (migrationResult.error) {
            result.failedMigrations++;
            result.errors.push(
              `Project ${project.name}: ${migrationResult.error}`,
            );
            result.projectResults.push({
              projectId: project.id,
              projectName: project.name,
              success: false,
              error: migrationResult.error,
            });
          } else {
            result.successfulMigrations++;
            result.projectResults.push({
              projectId: project.id,
              projectName: project.name,
              success: true,
            });
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          result.failedMigrations++;
          result.errors.push(`Project ${project.name}: ${errorMessage}`);
          result.projectResults.push({
            projectId: project.id,
            projectName: project.name,
            success: false,
            error: errorMessage,
          });
        }
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(`Migration failed: ${errorMessage}`);
      return result;
    }
  }

  async handleProjectDeletion(projectId: string): Promise<{
    success: boolean;
    error: string | null;
    cleanedUpItems: string[];
  }> {
    const cleanedUpItems: string[] = [];

    try {
      // Get subscription for the project
      const subscriptionResult =
        await subscriptionsService.getByProjectId(projectId);

      if (subscriptionResult.error) {
        return {
          success: false,
          error: subscriptionResult.error,
          cleanedUpItems,
        };
      }

      if (!subscriptionResult.data) {
        // No subscription to clean up
        return {
          success: true,
          error: null,
          cleanedUpItems: ['No billing subscription found'],
        };
      }

      const subscription = subscriptionResult.data;

      // Get all payment records for the subscription
      const paymentsResult = await paymentsService.getPaymentsBySubscription(
        subscription.id,
      );

      if (paymentsResult.error) {
        return {
          success: false,
          error: paymentsResult.error,
          cleanedUpItems,
        };
      }

      // Archive payment records instead of deleting them for audit trail
      if (paymentsResult.data && paymentsResult.data.length > 0) {
        const paymentIds = paymentsResult.data.map((p) => p.id);

        const { error: archiveError } = await supabase
          .from('payment_records')
          .update({
            status: 'cancelled' as PaymentStatus,
            updated_at: new Date().toISOString(),
            failure_reason: 'Project deleted',
          })
          .in('id', paymentIds);

        if (archiveError) {
          return {
            success: false,
            error: `Failed to archive payment records: ${archiveError.message}`,
            cleanedUpItems,
          };
        }

        cleanedUpItems.push(`Archived ${paymentIds.length} payment records`);
      }

      // Cancel the subscription instead of deleting for audit trail
      const cancelResult = await subscriptionsService.updateSubscriptionAccess({
        subscriptionId: subscription.id,
        status: 'cancelled',
        failureReason: 'Project deleted',
      });

      if (cancelResult.error) {
        return {
          success: false,
          error: `Failed to cancel subscription: ${cancelResult.error}`,
          cleanedUpItems,
        };
      }

      cleanedUpItems.push('Cancelled project subscription');

      return {
        success: true,
        error: null,
        cleanedUpItems,
      };
    } catch (error) {
      console.error('Handle project deletion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        cleanedUpItems,
      };
    }
  }

  async getProjectsWithBillingStatus(contractorId?: string): Promise<{
    data: ProjectWithBilling[];
    error: string | null;
  }> {
    try {
      let projectQuery = supabase.from('projects').select(`
          id,
          name,
          contractor_id,
          created_at
        `);

      if (contractorId) {
        projectQuery = projectQuery.eq('contractor_id', contractorId);
      }

      const { data: projects, error: projectsError } = await projectQuery;

      if (projectsError) {
        return { data: [], error: projectsError.message };
      }

      if (!projects || projects.length === 0) {
        return { data: [], error: null };
      }

      // Get all PMA subscriptions for these projects
      const projectIds = projects.map((p) => p.id);

      // Get PMA certificates and their subscriptions
      const { data: pmaSubscriptions } = await supabase
        .from('pma_subscriptions')
        .select(
          `
          *,
          pma_certificates!inner (
            id,
            project_id,
            pma_number,
            status
          )
        `,
        )
        .in('pma_certificates.project_id', projectIds);

      // Group PMA subscriptions by project
      const subscriptionsByProject = new Map<
        string,
        PmaSubscriptionWithDetails[]
      >();
      pmaSubscriptions?.forEach((sub) => {
        const projectId = sub.pma_certificates?.project_id;
        if (projectId) {
          if (!subscriptionsByProject.has(projectId)) {
            subscriptionsByProject.set(projectId, []);
          }
          subscriptionsByProject
            .get(projectId)!
            .push(sub as PmaSubscriptionWithDetails);
        }
      });

      // Get total PMA count per project
      const { data: pmaCounts } = await supabase
        .from('pma_certificates')
        .select('project_id')
        .in('project_id', projectIds)
        .is('deleted_at', null);

      const pmaCountsByProject = new Map<string, number>();
      pmaCounts?.forEach((pma) => {
        const count = pmaCountsByProject.get(pma.project_id!) || 0;
        pmaCountsByProject.set(pma.project_id!, count + 1);
      });

      // Combine project data with billing status
      const projectsWithBilling: ProjectWithBilling[] = projects.map(
        (project) => {
          const pmaSubscriptions = subscriptionsByProject.get(project.id) || [];
          const totalPmaCount = pmaCountsByProject.get(project.id) || 0;
          const activePmaSubscriptions = pmaSubscriptions.filter(
            (sub) => sub.status === 'active',
          ).length;

          let billingStatus: 'active' | 'pending' | 'none';
          let needsBillingSetup = false;

          if (totalPmaCount === 0) {
            billingStatus = 'none';
            needsBillingSetup = true;
          } else if (activePmaSubscriptions > 0) {
            billingStatus = 'active';
          } else {
            billingStatus = 'pending';
          }

          return {
            ...project,
            pmaSubscriptions,
            billingStatus,
            needsBillingSetup,
            totalPmaCount,
            activePmaSubscriptions,
          };
        },
      );

      return { data: projectsWithBilling, error: null };
    } catch (error) {
      console.error('Get projects with billing status error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async createProjectWithBilling(params: {
    projectData: {
      name: string;
      code?: string | null;
      agency_id?: string | null;
      location?: string | null;
      state?: Database['public']['Enums']['state_code'] | null;
      start_date?: string | null;
      end_date?: string | null;
      status?: string | null;
      contractor_id: string;
    };
    billingOptions?: {
      autoActivate?: boolean;
      gracePeriodDays?: number;
    };
  }): Promise<{
    data: {
      project: Project;
      pmaSubscriptions: PmaSubscriptionWithDetails[];
    } | null;
    error: string | null;
  }> {
    try {
      const { projectData, billingOptions: _billingOptions = {} } = params;

      // Create project first
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (projectError || !project) {
        return {
          data: null,
          error: projectError?.message || 'Failed to create project',
        };
      }

      // Return project with empty PMA subscriptions
      // PMA subscriptions will be created later when PMA certificates are added
      return {
        data: {
          project,
          pmaSubscriptions: [],
        },
        error: null,
      };
    } catch (error) {
      console.error('Create project with billing error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async validateProjectBillingIntegrity(projectId?: string): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      let projectQuery = supabase.from('projects').select(`
          id,
          name,
          contractor_id,
          created_at
        `);

      if (projectId) {
        projectQuery = projectQuery.eq('id', projectId);
      }

      const { data: projects } = await projectQuery;

      if (!projects || projects.length === 0) {
        return {
          isValid: true,
          issues: ['No projects found'],
          recommendations: [],
        };
      }

      for (const project of projects) {
        // Check if project has PMA certificates
        const { data: pmaCertificates } = await supabase
          .from('pma_certificates')
          .select('id')
          .eq('project_id', project.id)
          .is('deleted_at', null);

        if (!pmaCertificates || pmaCertificates.length === 0) {
          issues.push(`Project "${project.name}" has no PMA certificates`);
          recommendations.push(
            `Add PMA certificates for project "${project.name}"`,
          );
          continue;
        }

        // Check PMA subscriptions for each certificate
        const pmaSubscriptionsResult =
          await pmaSubscriptionsService.getByProjectId(project.id);

        if (pmaSubscriptionsResult.error) {
          issues.push(
            `Failed to check PMA subscriptions for project "${project.name}"`,
          );
          continue;
        }

        const subscriptionMap = new Map(
          pmaSubscriptionsResult.data?.map(
            (sub: PmaSubscriptionWithDetails) => [sub.pma_certificate_id, sub],
          ) || [],
        );

        // Check each PMA certificate has a subscription
        for (const pmaCert of pmaCertificates) {
          const subscription = subscriptionMap.get(pmaCert.id);

          if (!subscription) {
            issues.push(
              `PMA certificate ${pmaCert.id} in project "${project.name}" has no billing subscription`,
            );
            recommendations.push(
              `Create PMA subscription for certificate in project "${project.name}"`,
            );
            continue;
          }

          // Check subscription validity
          if (!subscription.amount || subscription.amount <= 0) {
            issues.push(
              `PMA subscription for project "${project.name}" has invalid amount`,
            );
            recommendations.push(
              `Update amount for PMA subscription in project "${project.name}"`,
            );
          }

          if (!subscription.next_billing_date) {
            issues.push(
              `PMA subscription for project "${project.name}" has no next billing date`,
            );
            recommendations.push(
              `Set next billing date for PMA subscription in project "${project.name}"`,
            );
          }

          // Check for orphaned subscriptions
          if (subscription.contractor_id !== project.contractor_id) {
            issues.push(
              `PMA subscription for project "${project.name}" has contractor mismatch`,
            );
            recommendations.push(
              `Fix contractor association for PMA subscription in project "${project.name}"`,
            );
          }

          // Check for stale grace periods
          if (
            subscription.status === 'grace_period' &&
            subscription.grace_period_ends
          ) {
            const gracePeriodEnd = new Date(subscription.grace_period_ends);
            if (gracePeriodEnd < new Date()) {
              issues.push(
                `PMA subscription for project "${project.name}" has expired grace period`,
              );
              recommendations.push(
                `Suspend or reactivate PMA subscription for project "${project.name}"`,
              );
            }
          }
        }
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      console.error('Validate project billing integrity error:', error);
      return {
        isValid: false,
        issues: [
          `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ],
        recommendations: ['Review billing system configuration'],
      };
    }
  }
}

export const projectIntegrationService = new ProjectIntegrationService();
