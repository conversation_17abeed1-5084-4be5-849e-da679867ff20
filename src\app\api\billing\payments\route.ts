import { accessControlService } from '@/features/billing/services/access-control.service';
import { paymentsService } from '@/features/billing/services/payments.service';
import { subscriptionsService } from '@/features/billing/services/subscriptions.service';
import { hasPermission } from '@/lib/rbac';
import { getUser } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const createPaymentSchema = z.object({
  subscriptionId: z.string().uuid(),
  amount: z.number().positive(),
  contractorEmail: z.string().email(),
  contractorName: z.string().min(1),
  contractorMobile: z.string().optional(),
  description: z.string().min(1),
  redirectUrl: z.string().url().optional(),
  reference1: z.string().optional(),
  reference2: z.string().optional(),
  restoreAccess: z.boolean().default(true),
});

const syncPaymentSchema = z.object({
  paymentRecordId: z.string().uuid(),
  syncWithBillPlz: z.boolean().default(true),
});

export async function POST(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create payments' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = createPaymentSchema.parse(body);

    // Get subscription to validate ownership and access
    const subscriptionResult =
      await subscriptionsService.getSubscriptionWithAccess(
        validatedData.subscriptionId,
      );

    if (subscriptionResult.error || !subscriptionResult.data) {
      return NextResponse.json(
        { error: subscriptionResult.error || 'Subscription not found' },
        { status: 404 },
      );
    }

    const subscription = subscriptionResult.data;

    // Validate contractor access for non-admin users
    if (
      user.user_role === 'contractor' &&
      subscription.contractor_id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Cannot create payments for other contractor subscriptions' },
        { status: 403 },
      );
    }

    // Check if subscription requires payment
    if (subscription.status === 'active' && subscription.accessAllowed) {
      return NextResponse.json(
        {
          error: 'Subscription is already active and does not require payment',
        },
        { status: 400 },
      );
    }

    // Get current access status for context
    const accessCheck = await accessControlService.checkProjectAccess(
      subscription.contractor_id,
      subscription.project_id,
      'contractor',
    );

    // Create payment record with BillPlz bill
    const paymentResult = await paymentsService.createPaymentRecord({
      subscriptionId: validatedData.subscriptionId,
      amount: validatedData.amount,
      contractorEmail: validatedData.contractorEmail,
      contractorName: validatedData.contractorName,
      contractorMobile: validatedData.contractorMobile,
      description: validatedData.description,
      redirectUrl: validatedData.redirectUrl,
      reference1: validatedData.reference1,
      reference2: validatedData.reference2,
    });

    if (paymentResult.error || !paymentResult.data) {
      return NextResponse.json(
        { error: paymentResult.error || 'Failed to create payment' },
        { status: 400 },
      );
    }

    const paymentRecord = paymentResult.data;

    // Get BillPlz payment URL
    const paymentUrlResult = await paymentsService.getBillPlzPaymentUrl(
      paymentRecord.id,
    );

    // Log payment creation for audit trail
    console.log(
      `Payment created for subscription ${validatedData.subscriptionId} by user ${user.id}, current access: ${accessCheck.hasAccess}`,
    );

    return NextResponse.json(
      {
        data: {
          paymentRecord,
          paymentUrl: paymentUrlResult.url,
          subscription: {
            id: subscription.id,
            projectId: subscription.project_id,
            status: subscription.status,
            accessAllowed: subscription.accessAllowed,
          },
          accessContext: {
            currentAccess: accessCheck.hasAccess,
            reason: accessCheck.reason,
            willRestoreAccess: validatedData.restoreAccess,
          },
        },
        message: 'Payment created successfully',
      },
      { status: 201 },
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('POST /api/billing/payments error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const paymentRecordId = searchParams.get('paymentRecordId');
    const subscriptionId = searchParams.get('subscriptionId');
    const contractorId = searchParams.get('contractorId');

    // Handle payment status check
    if (action === 'status' && paymentRecordId) {
      const paymentResult =
        await paymentsService.getPaymentWithSubscription(paymentRecordId);

      if (paymentResult.error || !paymentResult.data) {
        return NextResponse.json(
          { error: paymentResult.error || 'Payment not found' },
          { status: 404 },
        );
      }

      const payment = paymentResult.data;

      // Validate access for non-admin users
      if (
        user.user_role === 'contractor' &&
        payment.subscription.contractor_id !== user.id
      ) {
        return NextResponse.json(
          { error: 'Cannot access other contractor payments' },
          { status: 403 },
        );
      }

      // Get current access status
      const accessCheck = await accessControlService.checkProjectAccess(
        payment.subscription.contractor_id,
        payment.subscription.project_id,
        'contractor',
      );

      return NextResponse.json({
        data: {
          payment,
          accessStatus: {
            hasAccess: accessCheck.hasAccess,
            reason: accessCheck.reason,
            message: accessCheck.message,
          },
          lastChecked: new Date().toISOString(),
        },
      });
    }

    // Handle payment synchronization
    if (action === 'sync' && paymentRecordId) {
      syncPaymentSchema.parse({
        paymentRecordId,
        syncWithBillPlz: searchParams.get('syncWithBillPlz') !== 'false',
      });

      // Get payment to validate access
      const paymentResult =
        await paymentsService.getPaymentWithSubscription(paymentRecordId);

      if (paymentResult.error || !paymentResult.data) {
        return NextResponse.json(
          { error: paymentResult.error || 'Payment not found' },
          { status: 404 },
        );
      }

      const payment = paymentResult.data;

      // Validate access for non-admin users
      if (
        user.user_role === 'contractor' &&
        payment.subscription.contractor_id !== user.id
      ) {
        return NextResponse.json(
          { error: 'Cannot sync other contractor payments' },
          { status: 403 },
        );
      }

      // Sync payment with BillPlz
      const syncResult =
        await paymentsService.syncPaymentWithBillPlz(paymentRecordId);

      if (syncResult.error) {
        return NextResponse.json({ error: syncResult.error }, { status: 500 });
      }

      // Get updated access status after sync
      const accessCheck = await accessControlService.checkProjectAccess(
        payment.subscription.contractor_id,
        payment.subscription.project_id,
        'contractor',
      );

      return NextResponse.json({
        data: {
          syncResult: syncResult.data,
          accessRestored: syncResult.data?.accessRestored || false,
          updatedAccessStatus: {
            hasAccess: accessCheck.hasAccess,
            reason: accessCheck.reason,
            message: accessCheck.message,
          },
          syncedAt: new Date().toISOString(),
        },
        message: 'Payment synchronized successfully',
      });
    }

    // Handle payment history by subscription
    if (subscriptionId) {
      const paymentsResult =
        await paymentsService.getPaymentsBySubscription(subscriptionId);

      if (paymentsResult.error) {
        return NextResponse.json(
          { error: paymentsResult.error },
          { status: 500 },
        );
      }

      // Validate subscription access for non-admin users
      if (user.user_role === 'contractor') {
        const subscriptionResult =
          await subscriptionsService.getSubscriptionWithAccess(subscriptionId);

        if (
          subscriptionResult.data &&
          subscriptionResult.data.contractor_id !== user.id
        ) {
          return NextResponse.json(
            { error: 'Cannot access other contractor payments' },
            { status: 403 },
          );
        }
      }

      return NextResponse.json({
        data: paymentsResult.data,
        meta: {
          count: paymentsResult.data.length,
          subscriptionId,
        },
      });
    }

    // Handle payment history by contractor
    if (contractorId) {
      // Validate contractor access for non-admin users
      if (user.user_role === 'contractor' && contractorId !== user.id) {
        return NextResponse.json(
          { error: 'Cannot access other contractor payments' },
          { status: 403 },
        );
      }

      const paymentsResult =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (paymentsResult.error) {
        return NextResponse.json(
          { error: paymentsResult.error },
          { status: 500 },
        );
      }

      return NextResponse.json({
        data: paymentsResult.data,
        meta: {
          count: paymentsResult.data.length,
          contractorId,
        },
      });
    }

    // Default: Get current user's payment history if contractor
    if (user.user_role === 'contractor') {
      const paymentsResult = await paymentsService.getContractorPaymentHistory(
        user.id,
      );

      if (paymentsResult.error) {
        return NextResponse.json(
          { error: paymentsResult.error },
          { status: 500 },
        );
      }

      return NextResponse.json({
        data: paymentsResult.data,
        meta: {
          count: paymentsResult.data.length,
          contractorId: user.id,
        },
      });
    }

    return NextResponse.json(
      { error: 'Invalid request parameters' },
      { status: 400 },
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('GET /api/billing/payments error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
