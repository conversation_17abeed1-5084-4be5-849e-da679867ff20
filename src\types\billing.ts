import { z } from 'zod';
import type { Database } from './database';

// ================================
// DATABASE TYPES (from generated types)
// ================================

// Database table types
export type ProjectSubscription =
  Database['public']['Tables']['project_subscriptions']['Row'];
export type ProjectSubscriptionInsert =
  Database['public']['Tables']['project_subscriptions']['Insert'];
export type ProjectSubscriptionUpdate =
  Database['public']['Tables']['project_subscriptions']['Update'];

export type PaymentRecord =
  Database['public']['Tables']['payment_records']['Row'];
export type PaymentRecordInsert =
  Database['public']['Tables']['payment_records']['Insert'];
export type PaymentRecordUpdate =
  Database['public']['Tables']['payment_records']['Update'];

export type PaymentMethod =
  Database['public']['Tables']['payment_methods']['Row'];
export type PaymentMethodInsert =
  Database['public']['Tables']['payment_methods']['Insert'];
export type PaymentMethodUpdate =
  Database['public']['Tables']['payment_methods']['Update'];

// Enum types
export type SubscriptionStatus =
  Database['public']['Enums']['subscription_status'];
export type PaymentStatus = Database['public']['Enums']['payment_status'];

// ================================
// BILLPLZ API TYPES
// ================================

// BillPlz Configuration
export interface BillPlzConfig {
  apiKey: string;
  collectionId: string;
  xSignatureKey: string;
  sandboxMode: boolean;
  webhookUrl?: string;
}

// BillPlz Bill Request (Create Bill)
export interface BillPlzCreateBillRequest {
  collection_id: string;
  email: string;
  mobile?: string;
  name: string;
  amount: number; // Amount in cents (RM 150.00 = 15000 cents)
  callback_url: string;
  description: string;
  due_at?: string; // ISO date string
  redirect_url?: string;
  reference_1_label?: string;
  reference_1?: string;
  reference_2_label?: string;
  reference_2?: string;
}

// BillPlz Bill Response (from API)
export interface BillPlzBill {
  id: string;
  collection_id: string;
  paid: boolean;
  state: 'overdue' | 'open' | 'paid';
  amount: number;
  paid_amount: number;
  due_at: string;
  email: string;
  mobile: string | null;
  name: string;
  url: string;
  reference_1_label: string | null;
  reference_1: string | null;
  reference_2_label: string | null;
  reference_2: string | null;
  redirect_url: string | null;
  callback_url: string;
  description: string;
  paid_at: string | null;
}

// BillPlz Error Response
export interface BillPlzError {
  error: {
    type: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

// BillPlz Webhook Payload (for future webhook handling)
export interface BillPlzWebhookPayload {
  id: string;
  collection_id: string;
  paid: boolean;
  state: 'overdue' | 'open' | 'paid';
  amount: number;
  paid_amount: number;
  paid_at: string | null;
  x_signature: string;
}

// ================================
// API RESPONSE TYPES
// ================================

export interface BillPlzApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  billplzError?: BillPlzError;
}

// ================================
// BUSINESS LOGIC TYPES
// ================================

// Subscription with calculated access status
export interface SubscriptionWithAccess extends ProjectSubscription {
  accessAllowed: boolean;
  isInGracePeriod: boolean;
  daysUntilSuspension?: number;
}

// Payment record with subscription details
export interface PaymentRecordWithSubscription extends PaymentRecord {
  subscription: ProjectSubscription;
  projectName?: string;
}

// Types for Supabase joined queries
export interface PaymentRecordJoined extends PaymentRecord {
  project_subscriptions: ProjectSubscription & {
    projects: {
      name: string;
    };
  };
}

// Monthly billing job data
export interface MonthlyBillingData {
  subscriptionId: string;
  projectId: string;
  contractorId: string;
  contractorEmail: string;
  contractorName: string;
  projectName: string;
  amount: number;
  nextBillingDate: string;
}

// Grace period check data
export interface GracePeriodCheckData {
  subscriptionId: string;
  projectId: string;
  contractorId: string;
  gracePeriodEnds: string;
  daysRemaining: number;
}

// ================================
// FORM VALIDATION SCHEMAS
// ================================

// Payment form validation
export const paymentFormSchema = z.object({
  subscriptionId: z.string().uuid('Invalid subscription ID'),
  amount: z
    .number()
    .positive('Amount must be positive')
    .min(1, 'Minimum amount is RM 1.00'),
  contractorEmail: z
    .string()
    .email('Invalid email address')
    .min(1, 'Email is required'),
  contractorName: z
    .string()
    .min(1, 'Contractor name is required')
    .max(100, 'Name too long'),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(200, 'Description too long'),
  redirectUrl: z.string().url('Invalid redirect URL').optional(),
});

export type PaymentFormData = z.infer<typeof paymentFormSchema>;

// Subscription update schema
export const subscriptionUpdateSchema = z.object({
  status: z.enum([
    'active',
    'pending_payment',
    'grace_period',
    'cancelled',
    'suspended',
  ]),
  nextBillingDate: z.string().datetime().optional(),
  gracePeriodEnds: z.string().datetime().optional().nullable(),
});

export type SubscriptionUpdateData = z.infer<typeof subscriptionUpdateSchema>;

// Payment method schema
export const paymentMethodSchema = z.object({
  contractorId: z.string().uuid('Invalid contractor ID'),
  billplzCollectionId: z
    .string()
    .min(1, 'Collection ID is required')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Invalid collection ID format'),
  isDefault: z.boolean().default(true),
});

export type PaymentMethodData = z.infer<typeof paymentMethodSchema>;

// ================================
// UTILITY TYPES
// ================================

// Billing summary for dashboard
export interface BillingSummary {
  totalActiveSubscriptions: number;
  totalMonthlyAmount: number;
  upcomingPayments: number;
  overduePayments: number;
  subscriptionsInGracePeriod: number;
}

// Payment retry configuration
export interface PaymentRetryConfig {
  maxRetries: number;
  retryIntervalDays: number;
  gracePeriodDays: number;
}

// Billing notification types
export type BillingNotificationType =
  | 'payment_due'
  | 'payment_failed'
  | 'grace_period_started'
  | 'grace_period_ending'
  | 'subscription_suspended'
  | 'payment_successful';

export interface BillingNotification {
  type: BillingNotificationType;
  subscriptionId: string;
  projectId: string;
  contractorId: string;
  message: string;
  actionRequired: boolean;
  dueDate?: string;
}

// ================================
// CONSTANTS
// ================================

export const BILLING_CONSTANTS = {
  // Amounts (in MYR)
  MONTHLY_SUBSCRIPTION_AMOUNT: 150.0,

  // Grace period (in days)
  GRACE_PERIOD_DAYS: 7,

  // BillPlz amount conversion (MYR to cents)
  MYR_TO_CENTS_MULTIPLIER: 100,

  // Billing cycle
  DEFAULT_BILLING_CYCLE: 'monthly' as const,

  // Default currency
  DEFAULT_CURRENCY: 'MYR' as const,

  // Status that allow project access
  ACCESS_ALLOWED_STATUSES: ['active', 'grace_period'] as SubscriptionStatus[],

  // Payment retry settings
  PAYMENT_RETRY: {
    MAX_RETRIES: 3,
    RETRY_INTERVAL_DAYS: 2,
  } as const,
} as const;

// ================================
// TYPE GUARDS
// ================================

export function isSubscriptionActive(status: SubscriptionStatus): boolean {
  return BILLING_CONSTANTS.ACCESS_ALLOWED_STATUSES.includes(status);
}

export function isPaymentSuccessful(status: PaymentStatus): boolean {
  return status === 'paid';
}

export function isPaymentPending(status: PaymentStatus): boolean {
  return status === 'pending';
}

export function isPaymentFailed(status: PaymentStatus): boolean {
  return status === 'failed';
}

export function isBillPlzError(response: unknown): response is BillPlzError {
  return (
    typeof response === 'object' &&
    response !== null &&
    'error' in response &&
    typeof (response as { error: unknown }).error === 'object' &&
    (response as { error: unknown }).error !== null &&
    'message' in (response as { error: { message: unknown } }).error &&
    typeof (response as { error: { message: unknown } }).error.message ===
      'string'
  );
}

// ================================
// UTILITY FUNCTIONS
// ================================

// Convert MYR amount to BillPlz cents
export function convertMyrToCents(amount: number): number {
  return Math.round(amount * BILLING_CONSTANTS.MYR_TO_CENTS_MULTIPLIER);
}

// Convert BillPlz cents to MYR amount
export function convertCentsToMyr(cents: number): number {
  return cents / BILLING_CONSTANTS.MYR_TO_CENTS_MULTIPLIER;
}

// Calculate next billing date (monthly)
export function calculateNextBillingDate(currentDate: Date = new Date()): Date {
  const nextBilling = new Date(currentDate);
  nextBilling.setMonth(nextBilling.getMonth() + 1);
  return nextBilling;
}

// Calculate grace period end date
export function calculateGracePeriodEnd(failureDate: Date = new Date()): Date {
  const gracePeriodEnd = new Date(failureDate);
  gracePeriodEnd.setDate(
    gracePeriodEnd.getDate() + BILLING_CONSTANTS.GRACE_PERIOD_DAYS,
  );
  return gracePeriodEnd;
}

// Check if subscription allows project access
export function checkProjectAccess(subscription: ProjectSubscription): boolean {
  // Active status always allows access
  if (subscription.status === 'active') {
    return true;
  }

  // Grace period allows access if not expired
  if (
    subscription.status === 'grace_period' &&
    subscription.grace_period_ends
  ) {
    const now = new Date();
    const gracePeriodEnd = new Date(subscription.grace_period_ends);
    return now < gracePeriodEnd;
  }

  // All other statuses deny access
  return false;
}

// Get days remaining in grace period
export function getDaysRemainingInGracePeriod(
  gracePeriodEnds: string | null,
): number | null {
  if (!gracePeriodEnds) return null;

  const now = new Date();
  const endDate = new Date(gracePeriodEnds);
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
}
