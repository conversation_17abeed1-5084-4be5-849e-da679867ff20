

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."admin_access_mode" AS ENUM (
    'state',
    'project'
);


ALTER TYPE "public"."admin_access_mode" OWNER TO "postgres";


CREATE TYPE "public"."company_type" AS ENUM (
    'COMPETENT_FIRM',
    'NON_COMPETENT_FIRM',
    'OEM'
);


ALTER TYPE "public"."company_type" OWNER TO "postgres";


CREATE TYPE "public"."complaint_follow_up" AS ENUM (
    'in_progress',
    'pending_approval',
    'verified'
);


ALTER TYPE "public"."complaint_follow_up" OWNER TO "postgres";


CREATE TYPE "public"."complaint_status" AS ENUM (
    'open',
    'on_hold',
    'closed'
);


ALTER TYPE "public"."complaint_status" OWNER TO "postgres";


CREATE TYPE "public"."cp_type" AS ENUM (
    'CP1',
    'CP2',
    'CP3'
);


ALTER TYPE "public"."cp_type" OWNER TO "postgres";


CREATE TYPE "public"."maintenance_status" AS ENUM (
    'fully function',
    'broken'
);


ALTER TYPE "public"."maintenance_status" OWNER TO "postgres";


CREATE TYPE "public"."operation_type_enum" AS ENUM (
    'daily logs',
    'second schedule',
    'mantrap'
);


ALTER TYPE "public"."operation_type_enum" OWNER TO "postgres";


CREATE TYPE "public"."payment_status" AS ENUM (
    'pending',
    'paid',
    'failed',
    'cancelled',
    'refunded'
);


ALTER TYPE "public"."payment_status" OWNER TO "postgres";


CREATE TYPE "public"."pma_status" AS ENUM (
    'valid',
    'validating',
    'invalid'
);


ALTER TYPE "public"."pma_status" OWNER TO "postgres";


CREATE TYPE "public"."project_role" AS ENUM (
    'technician',
    'competent_person',
    'admin',
    'viewer'
);


ALTER TYPE "public"."project_role" OWNER TO "postgres";


CREATE TYPE "public"."project_user_status" AS ENUM (
    'invited',
    'accepted',
    'declined'
);


ALTER TYPE "public"."project_user_status" OWNER TO "postgres";


CREATE TYPE "public"."state_code" AS ENUM (
    'JH',
    'KD',
    'KT',
    'ML',
    'NS',
    'PH',
    'PN',
    'PK',
    'PL',
    'SB',
    'SW',
    'SL',
    'TR',
    'WP',
    'LBN',
    'PW',
    'OTH'
);


ALTER TYPE "public"."state_code" OWNER TO "postgres";


CREATE TYPE "public"."subscription_status" AS ENUM (
    'active',
    'pending_payment',
    'grace_period',
    'cancelled',
    'suspended'
);


ALTER TYPE "public"."subscription_status" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'contractor',
    'admin',
    'viewer'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_user_create_projects"("user_id_param" "uuid") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  user_record RECORD;
BEGIN
  SELECT user_role, admin_access_mode INTO user_record
  FROM users 
  WHERE id = user_id_param;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN (
    user_record.user_role = 'admin' OR
    (user_record.user_role = 'contractor' AND user_record.admin_access_mode = 'project')
  );
END;
$$;


ALTER FUNCTION "public"."can_user_create_projects"("user_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."enforce_project_admin_mode"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Allow project admin assignment if user is admin (any admin_access_mode) 
  -- OR if user is a contractor (for project creation purposes)
  IF NEW.role = 'admin' THEN
    IF NOT EXISTS (
      SELECT 1 FROM users 
      WHERE id = NEW.user_id 
      AND (user_role = 'admin' OR user_role = 'contractor')
    ) THEN
      RAISE EXCEPTION 'User % is not authorized to be a project admin', NEW.user_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."enforce_project_admin_mode"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_complaint_number"() RETURNS "text"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    current_year INTEGER;
    next_number INTEGER;
    complaint_number TEXT;
BEGIN
    -- Get current year
    current_year := EXTRACT(YEAR FROM CURRENT_DATE);
    
    -- Get the highest number for current year
    SELECT COALESCE(
        MAX(CAST(SPLIT_PART(number, '-', 3) AS INTEGER)), 0
    ) + 1
    INTO next_number
    FROM complaints
    WHERE number LIKE 'DCL-' || current_year || '-%';
    
    -- Format the complaint number with zero-padding
    complaint_number := 'DCL-' || current_year || '-' || LPAD(next_number::text, 4, '0');
    
    RETURN complaint_number;
END;
$$;


ALTER FUNCTION "public"."generate_complaint_number"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."grant_contractor_project_permissions"("user_id_param" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE users 
  SET admin_access_mode = 'project', 
      updated_at = now()
  WHERE id = user_id_param 
    AND user_role = 'contractor' 
    AND admin_access_mode IS NULL;
END;
$$;


ALTER FUNCTION "public"."grant_contractor_project_permissions"("user_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_complaint_number"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.number IS NULL OR NEW.number = '' THEN
        NEW.number := generate_complaint_number();
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_complaint_number"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."agencies" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "state" "public"."state_code",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid"
);


ALTER TABLE "public"."agencies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."competent_person" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "contractor_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "ic_no" "text" NOT NULL,
    "phone_no" "text",
    "address" "text",
    "cp_type" "public"."cp_type" NOT NULL,
    "cp_registeration_no" "text",
    "cp_registeration_cert" "text",
    "cert_exp_date" "date",
    "no_of_pma" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid"
);


ALTER TABLE "public"."competent_person" OWNER TO "postgres";


COMMENT ON TABLE "public"."competent_person" IS 'Competent persons associated with contractors. Each contractor can have multiple competent persons with different CP types (CP1, CP2, CP3).';



COMMENT ON COLUMN "public"."competent_person"."contractor_id" IS 'Foreign key to contractors table - one contractor can have many competent persons';



COMMENT ON COLUMN "public"."competent_person"."ic_no" IS 'Identity card number - must be unique across all competent persons';



COMMENT ON COLUMN "public"."competent_person"."cp_type" IS 'Competent person type: CP1, CP2, or CP3';



COMMENT ON COLUMN "public"."competent_person"."cp_registeration_cert" IS 'URL or file path to the competent person registration certificate';



COMMENT ON COLUMN "public"."competent_person"."no_of_pma" IS 'Number of PMAs associated with this competent person';



CREATE TABLE IF NOT EXISTS "public"."complaints" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" "text" NOT NULL,
    "number" "text" NOT NULL,
    "date" "date" NOT NULL,
    "contractor_name" "text",
    "location" "text",
    "no_pma_lif" "text",
    "description" "text",
    "expected_completion_date" "date",
    "involves_mantrap" boolean,
    "actual_completion_date" "date",
    "repair_completion_time" time without time zone,
    "cause_of_damage" "text",
    "correction_action" "text",
    "proof_of_repair_urls" "text"[],
    "repair_cost" numeric,
    "status" "public"."complaint_status" DEFAULT 'open'::"public"."complaint_status" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "contractor_id" "uuid",
    "project_id" "uuid",
    "pma_id" "uuid",
    "follow_up" "public"."complaint_follow_up" DEFAULT 'in_progress'::"public"."complaint_follow_up" NOT NULL,
    "verified_by" "text",
    "verified_date" "date",
    "before_repair_files" "text"[]
);


ALTER TABLE "public"."complaints" OWNER TO "postgres";


COMMENT ON COLUMN "public"."complaints"."follow_up" IS 'Follow-up status: in_progress (when complaint is created), pending_approval (when Section B is completed), verified (when admin approves)';



COMMENT ON COLUMN "public"."complaints"."verified_by" IS 'Name of the admin who verified the complaint';



COMMENT ON COLUMN "public"."complaints"."verified_date" IS 'Date when the complaint was verified by admin';



COMMENT ON COLUMN "public"."complaints"."before_repair_files" IS 'Array of URLs for files uploaded before repair work begins';



CREATE TABLE IF NOT EXISTS "public"."contractors" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "contractor_type" "public"."company_type" NOT NULL,
    "oem_name" "text",
    "hotline" "text",
    "is_active" boolean DEFAULT true NOT NULL,
    "code" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "appointed_oem_competent_firm" "text"
);


ALTER TABLE "public"."contractors" OWNER TO "postgres";


COMMENT ON COLUMN "public"."contractors"."appointed_oem_competent_firm" IS 'Name of the OEM or competent firm that has appointed this non-competent firm. Required for NON_COMPETENT_FIRM contractor types.';



CREATE TABLE IF NOT EXISTS "public"."maintenance_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "contractor_id" "uuid",
    "pma_id" "uuid",
    "operation_log_type" "text" NOT NULL,
    "log_date" "date" NOT NULL,
    "person_in_charge_name" "text",
    "person_in_charge_phone" "text",
    "description" "text",
    "project_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "status" "public"."maintenance_status" NOT NULL,
    "operation_type" "public"."operation_type_enum" DEFAULT 'daily logs'::"public"."operation_type_enum" NOT NULL
);


ALTER TABLE "public"."maintenance_logs" OWNER TO "postgres";


COMMENT ON COLUMN "public"."maintenance_logs"."status" IS 'Status of the equipment after maintenance: fully function or broken';



COMMENT ON COLUMN "public"."maintenance_logs"."operation_type" IS 'Type of maintenance operation: daily logs, second schedule, or mantrap';



CREATE TABLE IF NOT EXISTS "public"."payment_methods" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "contractor_id" "uuid" NOT NULL,
    "billplz_collection_id" "text" NOT NULL,
    "is_default" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payment_methods" OWNER TO "postgres";


COMMENT ON TABLE "public"."payment_methods" IS 'Contractor payment method configurations for BillPlz';



CREATE TABLE IF NOT EXISTS "public"."payment_records" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "billplz_bill_id" "text",
    "amount" numeric(10,2) NOT NULL,
    "status" "public"."payment_status" DEFAULT 'pending'::"public"."payment_status" NOT NULL,
    "paid_at" timestamp with time zone,
    "failure_reason" "text",
    "billplz_response" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "chk_paid_at_logic" CHECK (((("status" = 'paid'::"public"."payment_status") AND ("paid_at" IS NOT NULL)) OR (("status" <> 'paid'::"public"."payment_status") AND ("paid_at" IS NULL)))),
    CONSTRAINT "chk_payment_amount_positive" CHECK (("amount" > (0)::numeric))
);


ALTER TABLE "public"."payment_records" OWNER TO "postgres";


COMMENT ON TABLE "public"."payment_records" IS 'BillPlz payment transaction history and responses';



COMMENT ON COLUMN "public"."payment_records"."billplz_response" IS 'Complete BillPlz API response for debugging';



CREATE TABLE IF NOT EXISTS "public"."pma_certificates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "expiry_date" "date" NOT NULL,
    "status" "public"."pma_status" DEFAULT 'validating'::"public"."pma_status" NOT NULL,
    "file_url" "text",
    "pma_number" "text",
    "state" "public"."state_code",
    "project_id" "uuid",
    "location" "text",
    "total_repair_cost" numeric,
    "total_repair_time" interval,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "competent_person_id" "uuid",
    "inspection_date" "date"
);


ALTER TABLE "public"."pma_certificates" OWNER TO "postgres";


COMMENT ON COLUMN "public"."pma_certificates"."competent_person_id" IS 'Reference to competent_person table for the competent person associated with this PMA certificate';



COMMENT ON COLUMN "public"."pma_certificates"."inspection_date" IS 'Date when the PMA inspection was conducted';



CREATE TABLE IF NOT EXISTS "public"."project_invitations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "inviter_user_id" "uuid",
    "invitee_user_id" "uuid",
    "invitee_email" "text" NOT NULL,
    "role" "public"."project_role" NOT NULL,
    "token" "text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "expiry_date" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "responded_at" timestamp with time zone,
    "responded_by" "uuid",
    "supabase_user_id" "uuid"
);


ALTER TABLE "public"."project_invitations" OWNER TO "postgres";


COMMENT ON COLUMN "public"."project_invitations"."supabase_user_id" IS 'Supabase Auth user ID for the invited user, used for linking Supabase auth to project invitations';



CREATE TABLE IF NOT EXISTS "public"."project_subscriptions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "contractor_id" "uuid" NOT NULL,
    "amount" numeric(10,2) DEFAULT 150.00 NOT NULL,
    "currency" "text" DEFAULT 'MYR'::"text" NOT NULL,
    "status" "public"."subscription_status" DEFAULT 'pending_payment'::"public"."subscription_status" NOT NULL,
    "billing_cycle" "text" DEFAULT 'monthly'::"text" NOT NULL,
    "next_billing_date" timestamp with time zone,
    "grace_period_ends" timestamp with time zone,
    "access_allowed" boolean GENERATED ALWAYS AS (("status" = ANY (ARRAY['active'::"public"."subscription_status", 'grace_period'::"public"."subscription_status"]))) STORED,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "chk_amount_positive" CHECK (("amount" > (0)::numeric)),
    CONSTRAINT "chk_billing_cycle_valid" CHECK (("billing_cycle" = 'monthly'::"text")),
    CONSTRAINT "chk_currency_valid" CHECK (("currency" = 'MYR'::"text")),
    CONSTRAINT "chk_grace_period_logic" CHECK (((("status" = 'grace_period'::"public"."subscription_status") AND ("grace_period_ends" IS NOT NULL)) OR (("status" <> 'grace_period'::"public"."subscription_status") AND ("grace_period_ends" IS NULL))))
);


ALTER TABLE "public"."project_subscriptions" OWNER TO "postgres";


COMMENT ON TABLE "public"."project_subscriptions" IS 'Monthly subscription management for lift projects with access control';



COMMENT ON COLUMN "public"."project_subscriptions"."status" IS 'Subscription status controlling project access';



COMMENT ON COLUMN "public"."project_subscriptions"."grace_period_ends" IS 'End of 7-day grace period after payment failure';



COMMENT ON COLUMN "public"."project_subscriptions"."access_allowed" IS 'Computed field: true when status allows project access (active or grace_period)';



CREATE TABLE IF NOT EXISTS "public"."project_users" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "project_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "public"."project_role" NOT NULL,
    "assigned_date" "date" DEFAULT CURRENT_DATE,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "status" "public"."project_user_status" DEFAULT 'accepted'::"public"."project_user_status" NOT NULL
);


ALTER TABLE "public"."project_users" OWNER TO "postgres";


COMMENT ON COLUMN "public"."project_users"."status" IS 'Invitation status: invited (pending), accepted (active member), declined (rejected invitation)';



CREATE TABLE IF NOT EXISTS "public"."projects" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "contractor_id" "uuid",
    "agency_id" "uuid",
    "name" "text" NOT NULL,
    "code" "text",
    "state" "public"."state_code",
    "location" "text",
    "start_date" "date",
    "end_date" "date",
    "status" "text" DEFAULT 'active'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid"
);


ALTER TABLE "public"."projects" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "phone_number" "text",
    "email" "text" NOT NULL,
    "user_role" "public"."user_role" NOT NULL,
    "admin_access_mode" "public"."admin_access_mode",
    "monitoring_state" "public"."state_code",
    "contractor_id" "uuid",
    "onboarding_completed" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "created_by" "uuid",
    "updated_by" "uuid",
    "deleted_by" "uuid",
    "state" "public"."state_code",
    CONSTRAINT "chk_admin_mode_validity" CHECK ((("user_role" = 'admin'::"public"."user_role") OR (("user_role" = 'contractor'::"public"."user_role") AND (("admin_access_mode" IS NULL) OR ("admin_access_mode" = 'project'::"public"."admin_access_mode"))) OR (("user_role" <> ALL (ARRAY['admin'::"public"."user_role", 'contractor'::"public"."user_role"])) AND ("admin_access_mode" IS NULL)))),
    CONSTRAINT "chk_state_monitoring" CHECK (((("admin_access_mode" = 'state'::"public"."admin_access_mode") AND ("monitoring_state" IS NOT NULL)) OR (("admin_access_mode" <> 'state'::"public"."admin_access_mode") AND ("monitoring_state" IS NULL))))
);


ALTER TABLE "public"."users" OWNER TO "postgres";


COMMENT ON COLUMN "public"."users"."state" IS 'Optional state assignment for admin users - used for organization and filtering purposes';



ALTER TABLE ONLY "public"."agencies"
    ADD CONSTRAINT "agencies_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."agencies"
    ADD CONSTRAINT "agencies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "competent_person_ic_no_key" UNIQUE ("ic_no");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "competent_person_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "complaints_number_key" UNIQUE ("number");



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "complaints_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."contractors"
    ADD CONSTRAINT "contractors_code_key" UNIQUE ("code");



ALTER TABLE ONLY "public"."contractors"
    ADD CONSTRAINT "contractors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "maintenance_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment_methods"
    ADD CONSTRAINT "payment_methods_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment_records"
    ADD CONSTRAINT "payment_records_billplz_bill_id_key" UNIQUE ("billplz_bill_id");



ALTER TABLE ONLY "public"."payment_records"
    ADD CONSTRAINT "payment_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "pma_certificates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."project_subscriptions"
    ADD CONSTRAINT "project_subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "project_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "project_users_project_id_user_id_role_key" UNIQUE ("project_id", "user_id", "role");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "projects_code_key" UNIQUE ("code");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "projects_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."project_subscriptions"
    ADD CONSTRAINT "uq_project_subscriptions_project_id" UNIQUE ("project_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_unique" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_agencies_deleted_at" ON "public"."agencies" USING "btree" ("deleted_at");



CREATE INDEX "idx_agencies_state" ON "public"."agencies" USING "btree" ("state");



CREATE INDEX "idx_competent_person_cert_exp_date" ON "public"."competent_person" USING "btree" ("cert_exp_date");



CREATE INDEX "idx_competent_person_contractor_id" ON "public"."competent_person" USING "btree" ("contractor_id");



CREATE INDEX "idx_competent_person_cp_type" ON "public"."competent_person" USING "btree" ("cp_type");



CREATE INDEX "idx_competent_person_deleted_at" ON "public"."competent_person" USING "btree" ("deleted_at");



CREATE INDEX "idx_competent_person_ic_no" ON "public"."competent_person" USING "btree" ("ic_no");



CREATE INDEX "idx_complaints_contractor_id" ON "public"."complaints" USING "btree" ("contractor_id");



CREATE INDEX "idx_complaints_deleted_at" ON "public"."complaints" USING "btree" ("deleted_at");



CREATE INDEX "idx_complaints_follow_up" ON "public"."complaints" USING "btree" ("follow_up");



CREATE INDEX "idx_complaints_pma_id" ON "public"."complaints" USING "btree" ("pma_id");



CREATE INDEX "idx_complaints_project_id" ON "public"."complaints" USING "btree" ("project_id");



CREATE INDEX "idx_complaints_verified_by" ON "public"."complaints" USING "btree" ("verified_by");



CREATE INDEX "idx_complaints_verified_date" ON "public"."complaints" USING "btree" ("verified_date");



CREATE INDEX "idx_contractors_deleted_at" ON "public"."contractors" USING "btree" ("deleted_at");



CREATE INDEX "idx_maintenance_logs_contractor_id" ON "public"."maintenance_logs" USING "btree" ("contractor_id");



CREATE INDEX "idx_maintenance_logs_deleted_at" ON "public"."maintenance_logs" USING "btree" ("deleted_at");



CREATE INDEX "idx_maintenance_logs_operation_type_enum" ON "public"."maintenance_logs" USING "btree" ("operation_type");



CREATE INDEX "idx_maintenance_logs_pma_id" ON "public"."maintenance_logs" USING "btree" ("pma_id");



CREATE INDEX "idx_maintenance_logs_status" ON "public"."maintenance_logs" USING "btree" ("status");



CREATE INDEX "idx_payment_methods_contractor" ON "public"."payment_methods" USING "btree" ("contractor_id");



CREATE INDEX "idx_payment_records_billplz_id" ON "public"."payment_records" USING "btree" ("billplz_bill_id") WHERE ("billplz_bill_id" IS NOT NULL);



CREATE INDEX "idx_payment_records_created_at" ON "public"."payment_records" USING "btree" ("created_at" DESC);



CREATE INDEX "idx_payment_records_subscription_status" ON "public"."payment_records" USING "btree" ("subscription_id", "status");



CREATE INDEX "idx_pma_cert_competent_person_id" ON "public"."pma_certificates" USING "btree" ("competent_person_id");



CREATE INDEX "idx_pma_cert_deleted_at" ON "public"."pma_certificates" USING "btree" ("deleted_at");



CREATE INDEX "idx_pma_cert_inspection_date" ON "public"."pma_certificates" USING "btree" ("inspection_date");



CREATE INDEX "idx_pma_cert_project_id" ON "public"."pma_certificates" USING "btree" ("project_id");



CREATE INDEX "idx_project_inv_email" ON "public"."project_invitations" USING "btree" ("invitee_email");



CREATE INDEX "idx_project_inv_invitee_user" ON "public"."project_invitations" USING "btree" ("invitee_user_id");



CREATE INDEX "idx_project_inv_project_id" ON "public"."project_invitations" USING "btree" ("project_id");



CREATE INDEX "idx_project_inv_status" ON "public"."project_invitations" USING "btree" ("status");



CREATE INDEX "idx_project_invitations_supabase_user_id" ON "public"."project_invitations" USING "btree" ("supabase_user_id");



CREATE INDEX "idx_project_subscriptions_access_allowed" ON "public"."project_subscriptions" USING "btree" ("access_allowed", "contractor_id") WHERE ("access_allowed" = true);



CREATE INDEX "idx_project_subscriptions_contractor_status" ON "public"."project_subscriptions" USING "btree" ("contractor_id", "status");



CREATE INDEX "idx_project_subscriptions_grace_period" ON "public"."project_subscriptions" USING "btree" ("grace_period_ends") WHERE ("status" = 'grace_period'::"public"."subscription_status");



CREATE INDEX "idx_project_subscriptions_next_billing" ON "public"."project_subscriptions" USING "btree" ("next_billing_date") WHERE ("status" = 'active'::"public"."subscription_status");



CREATE INDEX "idx_project_users_deleted_at" ON "public"."project_users" USING "btree" ("deleted_at");



CREATE INDEX "idx_project_users_is_active" ON "public"."project_users" USING "btree" ("is_active");



CREATE INDEX "idx_project_users_project_id" ON "public"."project_users" USING "btree" ("project_id");



CREATE INDEX "idx_project_users_role" ON "public"."project_users" USING "btree" ("role");



CREATE INDEX "idx_project_users_status" ON "public"."project_users" USING "btree" ("status");



CREATE INDEX "idx_project_users_user_id" ON "public"."project_users" USING "btree" ("user_id");



CREATE INDEX "idx_projects_agency_id" ON "public"."projects" USING "btree" ("agency_id");



CREATE INDEX "idx_projects_contractor_id" ON "public"."projects" USING "btree" ("contractor_id");



CREATE INDEX "idx_projects_deleted_at" ON "public"."projects" USING "btree" ("deleted_at");



CREATE INDEX "idx_users_contractor_id" ON "public"."users" USING "btree" ("contractor_id");



CREATE INDEX "idx_users_deleted_at" ON "public"."users" USING "btree" ("deleted_at");



CREATE INDEX "idx_users_monitoring_state" ON "public"."users" USING "btree" ("monitoring_state");



CREATE INDEX "idx_users_user_role" ON "public"."users" USING "btree" ("user_role");



CREATE OR REPLACE TRIGGER "trg_validate_project_admin" BEFORE INSERT OR UPDATE ON "public"."project_users" FOR EACH ROW EXECUTE FUNCTION "public"."enforce_project_admin_mode"();



CREATE OR REPLACE TRIGGER "trigger_set_complaint_number" BEFORE INSERT ON "public"."complaints" FOR EACH ROW EXECUTE FUNCTION "public"."set_complaint_number"();



CREATE OR REPLACE TRIGGER "trigger_update_project_subscriptions_updated_at" BEFORE UPDATE ON "public"."project_subscriptions" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "competent_person_contractor_id_fkey" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "complaints_contractor_id_fkey" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "complaints_pma_id_fkey" FOREIGN KEY ("pma_id") REFERENCES "public"."pma_certificates"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "complaints_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."agencies"
    ADD CONSTRAINT "fk_agencies_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."agencies"
    ADD CONSTRAINT "fk_agencies_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."agencies"
    ADD CONSTRAINT "fk_agencies_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "fk_competent_person_contractor" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "fk_competent_person_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "fk_competent_person_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."competent_person"
    ADD CONSTRAINT "fk_competent_person_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "fk_complaints_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "fk_complaints_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."complaints"
    ADD CONSTRAINT "fk_complaints_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."contractors"
    ADD CONSTRAINT "fk_contractors_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."contractors"
    ADD CONSTRAINT "fk_contractors_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."contractors"
    ADD CONSTRAINT "fk_contractors_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "fk_maint_logs_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "fk_maint_logs_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "fk_maint_logs_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."payment_methods"
    ADD CONSTRAINT "fk_payment_methods_contractor_id" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payment_records"
    ADD CONSTRAINT "fk_payment_records_subscription_id" FOREIGN KEY ("subscription_id") REFERENCES "public"."project_subscriptions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "fk_pma_cert_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "fk_pma_cert_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "fk_pma_cert_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "fk_proj_inv_inviter" FOREIGN KEY ("inviter_user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "fk_proj_inv_responder" FOREIGN KEY ("responded_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_subscriptions"
    ADD CONSTRAINT "fk_project_subscriptions_contractor_id" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_subscriptions"
    ADD CONSTRAINT "fk_project_subscriptions_project_id" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "fk_project_users_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "fk_project_users_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "fk_project_users_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "fk_projects_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "fk_projects_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "fk_projects_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "fk_users_created_by" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "fk_users_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "fk_users_updated_by" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "maintenance_logs_contractor_id_fkey" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "maintenance_logs_pma_id_fkey" FOREIGN KEY ("pma_id") REFERENCES "public"."pma_certificates"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."maintenance_logs"
    ADD CONSTRAINT "maintenance_logs_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "pma_certificates_competent_person_id_fkey" FOREIGN KEY ("competent_person_id") REFERENCES "public"."competent_person"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "pma_certificates_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pma_certificates"
    ADD CONSTRAINT "pma_certificates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_invitee_user_id_fkey" FOREIGN KEY ("invitee_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_inviter_user_id_fkey" FOREIGN KEY ("inviter_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_responded_by_fkey" FOREIGN KEY ("responded_by") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."project_invitations"
    ADD CONSTRAINT "project_invitations_supabase_user_id_fkey" FOREIGN KEY ("supabase_user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "project_users_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."project_users"
    ADD CONSTRAINT "project_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "projects_agency_id_fkey" FOREIGN KEY ("agency_id") REFERENCES "public"."agencies"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."projects"
    ADD CONSTRAINT "projects_contractor_id_fkey" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_contractor_id_fkey" FOREIGN KEY ("contractor_id") REFERENCES "public"."contractors"("id");





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";





GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";































































































































































GRANT ALL ON FUNCTION "public"."can_user_create_projects"("user_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_user_create_projects"("user_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_user_create_projects"("user_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."enforce_project_admin_mode"() TO "anon";
GRANT ALL ON FUNCTION "public"."enforce_project_admin_mode"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."enforce_project_admin_mode"() TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_complaint_number"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_complaint_number"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_complaint_number"() TO "service_role";



GRANT ALL ON FUNCTION "public"."grant_contractor_project_permissions"("user_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."grant_contractor_project_permissions"("user_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."grant_contractor_project_permissions"("user_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_complaint_number"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_complaint_number"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_complaint_number"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."agencies" TO "anon";
GRANT ALL ON TABLE "public"."agencies" TO "authenticated";
GRANT ALL ON TABLE "public"."agencies" TO "service_role";



GRANT ALL ON TABLE "public"."competent_person" TO "anon";
GRANT ALL ON TABLE "public"."competent_person" TO "authenticated";
GRANT ALL ON TABLE "public"."competent_person" TO "service_role";



GRANT ALL ON TABLE "public"."complaints" TO "anon";
GRANT ALL ON TABLE "public"."complaints" TO "authenticated";
GRANT ALL ON TABLE "public"."complaints" TO "service_role";



GRANT ALL ON TABLE "public"."contractors" TO "anon";
GRANT ALL ON TABLE "public"."contractors" TO "authenticated";
GRANT ALL ON TABLE "public"."contractors" TO "service_role";



GRANT ALL ON TABLE "public"."maintenance_logs" TO "anon";
GRANT ALL ON TABLE "public"."maintenance_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."maintenance_logs" TO "service_role";



GRANT ALL ON TABLE "public"."payment_methods" TO "anon";
GRANT ALL ON TABLE "public"."payment_methods" TO "authenticated";
GRANT ALL ON TABLE "public"."payment_methods" TO "service_role";



GRANT ALL ON TABLE "public"."payment_records" TO "anon";
GRANT ALL ON TABLE "public"."payment_records" TO "authenticated";
GRANT ALL ON TABLE "public"."payment_records" TO "service_role";



GRANT ALL ON TABLE "public"."pma_certificates" TO "anon";
GRANT ALL ON TABLE "public"."pma_certificates" TO "authenticated";
GRANT ALL ON TABLE "public"."pma_certificates" TO "service_role";



GRANT ALL ON TABLE "public"."project_invitations" TO "anon";
GRANT ALL ON TABLE "public"."project_invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."project_invitations" TO "service_role";



GRANT ALL ON TABLE "public"."project_subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."project_subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."project_subscriptions" TO "service_role";



GRANT ALL ON TABLE "public"."project_users" TO "anon";
GRANT ALL ON TABLE "public"."project_users" TO "authenticated";
GRANT ALL ON TABLE "public"."project_users" TO "service_role";



GRANT ALL ON TABLE "public"."projects" TO "anon";
GRANT ALL ON TABLE "public"."projects" TO "authenticated";
GRANT ALL ON TABLE "public"."projects" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";






























RESET ALL;
