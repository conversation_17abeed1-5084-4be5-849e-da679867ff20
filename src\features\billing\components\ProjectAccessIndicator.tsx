'use client';

import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import type { SubscriptionStatus } from '@/types/billing';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import { AlertTriangle, CheckCircle, Clock, XCircle } from 'lucide-react';

/**
 * Access status types for visual indication
 */
export type AccessState = 'active' | 'grace' | 'suspended' | 'expired';

export interface ProjectAccessIndicatorProps {
  status: SubscriptionStatus;
  gracePeriodEnds?: string | null;
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Visual access status component with state-aware styling and tooltips
 */
export function ProjectAccessIndicator({
  status,
  gracePeriodEnds,
  className,
  showLabel = true,
  size = 'md',
}: ProjectAccessIndicatorProps) {
  const accessState = getAccessState(status, gracePeriodEnds);
  const daysRemaining = getDaysRemainingInGracePeriod(gracePeriodEnds || null);

  const config = getAccessConfig(accessState, daysRemaining);

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5',
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  if (!showLabel) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn('inline-flex items-center', className)}>
              <config.icon className={cn(iconSizes[size], config.iconColor)} />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="font-medium">{config.label}</p>
            <p className="text-xs text-muted-foreground mt-1">
              {config.description}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant={config.variant}
            className={cn(
              'inline-flex items-center gap-1.5 font-medium',
              sizeClasses[size],
              config.className,
              className,
            )}
          >
            <config.icon className={iconSizes[size]} />
            {config.label}
            {daysRemaining !== null &&
              daysRemaining > 0 &&
              accessState === 'grace' && (
                <span className="text-xs">({daysRemaining}d)</span>
              )}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="font-medium">{config.label}</p>
          <p className="text-xs text-muted-foreground mt-1">
            {config.description}
          </p>
          {accessState === 'grace' && daysRemaining !== null && (
            <p className="text-xs text-yellow-600 mt-1">
              Access expires in {daysRemaining} day
              {daysRemaining !== 1 ? 's' : ''}
            </p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Determine access state from subscription status and grace period
 */
function getAccessState(
  status: SubscriptionStatus,
  gracePeriodEnds?: string | null,
): AccessState {
  switch (status) {
    case 'active':
      return 'active';
    case 'grace_period':
      if (gracePeriodEnds) {
        const now = new Date();
        const endDate = new Date(gracePeriodEnds);
        return now < endDate ? 'grace' : 'expired';
      }
      return 'expired';
    case 'suspended':
    case 'cancelled':
      return 'suspended';
    case 'pending_payment':
    default:
      return 'suspended';
  }
}

/**
 * Get configuration for each access state
 */
function getAccessConfig(state: AccessState, daysRemaining?: number | null) {
  switch (state) {
    case 'active':
      return {
        icon: CheckCircle,
        iconColor: 'text-green-500',
        label: 'Active',
        description: 'Full access to project features',
        variant: 'default' as const,
        className:
          'bg-green-100 text-green-800 hover:bg-green-200 border-green-300',
      };
    case 'grace':
      return {
        icon: Clock,
        iconColor: 'text-yellow-500',
        label: 'Grace Period',
        description: daysRemaining
          ? `Access expires in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}. Payment required to continue.`
          : 'Limited time access. Payment required to continue.',
        variant: 'secondary' as const,
        className:
          'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300',
      };
    case 'suspended':
      return {
        icon: XCircle,
        iconColor: 'text-red-500',
        label: 'No Access',
        description: 'Payment required to restore access',
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-300',
      };
    case 'expired':
      return {
        icon: AlertTriangle,
        iconColor: 'text-red-500',
        label: 'Expired',
        description: 'Grace period ended. Payment required to restore access.',
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-300',
      };
    default:
      return {
        icon: XCircle,
        iconColor: 'text-gray-500',
        label: 'Unknown',
        description: 'Status unknown',
        variant: 'outline' as const,
        className: '',
      };
  }
}
