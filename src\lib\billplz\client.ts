/**
 * BillPlz API Client
 * Comprehensive BillPlz payment gateway integration with error handling and logging
 */

import {
  getBillPlzConfigSingleton,
  getApiEndpoint,
  getMaskedApiKey,
  BILLPLZ_ENDPOINTS,
  RATE_LIMIT_CONFIG,
  LOGGING_CONFIG,
} from './config';
import {
  convertMyrToCents,
  generateBillReference,
  validateEmail,
  validateMalaysianMobile,
  formatMalaysianMobile,
  validateAmount,
  formatDateForBillPlz,
  calculateDueDate,
  generateRequestId,
  maskSensitiveData,
  retryWithBackoff,
} from './utils';
import type {
  BillPlzCreateBillRequest,
  BillPlzBill,
  BillPlzCreateCollectionRequest,
  BillPlzCollection,
  BillPlzTransaction,
  BillPlzError,
  BillPlzApiError,
  BillPlzNetworkError,
  BillPlzValidationError,
  BillPlzRateLimitError,
  BillPlzApiResponse,
  RateLimitState,
  RequestOptions,
  HttpMethod,
  RequestLog,
  ResponseLog,
  ErrorLog,
} from './types';
import { HTTP_STATUS_CODES } from './types';

// ================================
// BILLPLZ CLIENT CLASS
// ================================

export class BillPlzClient {
  private readonly config = getBillPlzConfigSingleton();
  private readonly rateLimitState: RateLimitState = {
    requests: [],
    circuitBreakerFailures: 0,
    isCircuitOpen: false,
    lastFailureTime: 0,
  };

  constructor() {
    this.logInfo('BillPlz client initialized', {
      sandboxMode: this.config.sandboxMode,
      apiVersion: this.config.apiVersion,
      baseUrl: this.config.baseUrl,
    });
  }

  // ================================
  // BILL MANAGEMENT METHODS
  // ================================

  /**
   * Create a new BillPlz bill
   */
  async createBill(params: {
    contractorEmail: string;
    contractorName: string;
    contractorMobile?: string;
    amount: number; // In MYR
    description: string;
    projectId?: string;
    subscriptionId?: string;
    contractorId?: string;
    dueInDays?: number;
    callbackUrl?: string;
    redirectUrl?: string;
  }): Promise<BillPlzApiResponse<BillPlzBill>> {
    const requestId = generateRequestId();

    try {
      // Validate inputs
      this.validateBillCreationParams(params);

      // Generate bill reference
      const reference = generateBillReference({
        projectId: params.projectId,
        subscriptionId: params.subscriptionId,
        contractorId: params.contractorId,
      });

      // Convert amount to cents
      const amountInCents = convertMyrToCents(params.amount);

      // Calculate due date
      const dueDate = calculateDueDate(params.dueInDays);

      // Prepare request payload
      const billRequest: BillPlzCreateBillRequest = {
        collection_id: this.config.collectionId,
        email: params.contractorEmail,
        mobile: params.contractorMobile
          ? formatMalaysianMobile(params.contractorMobile)
          : undefined,
        name: params.contractorName,
        amount: amountInCents,
        callback_url: params.callbackUrl || this.config.webhookUrl || '',
        description: params.description,
        due_at: formatDateForBillPlz(dueDate),
        redirect_url: params.redirectUrl,
        reference_1_label: 'Subscription Reference',
        reference_1: reference,
        reference_2_label: 'Project ID',
        reference_2: params.projectId,
      };

      this.logRequest(requestId, 'POST', BILLPLZ_ENDPOINTS.BILLS, billRequest);

      // Make API request
      const response = await this.makeRequest<BillPlzBill>(
        'POST',
        BILLPLZ_ENDPOINTS.BILLS,
        {
          body: billRequest,
        },
      );

      this.logInfo(`Bill created successfully: ${response.data?.id}`, {
        requestId,
        billId: response.data?.id,
        reference,
        amount: params.amount,
      });

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.BILLS, error);
      throw error;
    }
  }

  /**
   * Get bill details by ID
   */
  async getBill(billId: string): Promise<BillPlzApiResponse<BillPlzBill>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.BILL_BY_ID(billId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzBill>('GET', endpoint);

      this.logInfo(`Bill retrieved: ${billId}`, {
        requestId,
        billId,
        state: response.data?.state,
        paid: response.data?.paid,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.BILL_BY_ID(billId),
        error,
      );
      throw error;
    }
  }

  /**
   * Delete (cancel) a bill
   */
  async deleteBill(billId: string): Promise<BillPlzApiResponse<void>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.BILL_BY_ID(billId);
      this.logRequest(requestId, 'DELETE', endpoint);

      const response = await this.makeRequest<void>('DELETE', endpoint);

      this.logInfo(`Bill deleted: ${billId}`, {
        requestId,
        billId,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'DELETE',
        BILLPLZ_ENDPOINTS.BILL_BY_ID(billId),
        error,
      );
      throw error;
    }
  }

  /**
   * Get bill transactions
   */
  async getBillTransactions(
    billId: string,
  ): Promise<BillPlzApiResponse<BillPlzTransaction[]>> {
    const requestId = generateRequestId();

    try {
      if (!billId || typeof billId !== 'string') {
        throw this.createValidationError(
          'Bill ID is required and must be a string',
        );
      }

      const endpoint = BILLPLZ_ENDPOINTS.TRANSACTIONS(billId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzTransaction[]>(
        'GET',
        endpoint,
      );

      this.logInfo(`Bill transactions retrieved: ${billId}`, {
        requestId,
        billId,
        transactionCount: response.data?.length || 0,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.TRANSACTIONS(billId),
        error,
      );
      throw error;
    }
  }

  // ================================
  // COLLECTION MANAGEMENT METHODS
  // ================================

  /**
   * Create a new collection (admin only)
   */
  async createCollection(params: {
    title: string;
    logo?: string;
  }): Promise<BillPlzApiResponse<BillPlzCollection>> {
    const requestId = generateRequestId();

    try {
      if (!params.title || typeof params.title !== 'string') {
        throw this.createValidationError('Collection title is required');
      }

      const collectionRequest: BillPlzCreateCollectionRequest = {
        title: params.title,
        logo: params.logo,
      };

      this.logRequest(
        requestId,
        'POST',
        BILLPLZ_ENDPOINTS.COLLECTIONS,
        collectionRequest,
      );

      const response = await this.makeRequest<BillPlzCollection>(
        'POST',
        BILLPLZ_ENDPOINTS.COLLECTIONS,
        {
          body: collectionRequest,
        },
      );

      this.logInfo(`Collection created: ${response.data?.id}`, {
        requestId,
        collectionId: response.data?.id,
        title: params.title,
      });

      return response;
    } catch (error) {
      this.logError(requestId, 'POST', BILLPLZ_ENDPOINTS.COLLECTIONS, error);
      throw error;
    }
  }

  /**
   * Get collection details
   */
  async getCollection(
    collectionId: string,
  ): Promise<BillPlzApiResponse<BillPlzCollection>> {
    const requestId = generateRequestId();

    try {
      if (!collectionId || typeof collectionId !== 'string') {
        throw this.createValidationError('Collection ID is required');
      }

      const endpoint = BILLPLZ_ENDPOINTS.COLLECTION_BY_ID(collectionId);
      this.logRequest(requestId, 'GET', endpoint);

      const response = await this.makeRequest<BillPlzCollection>(
        'GET',
        endpoint,
      );

      this.logInfo(`Collection retrieved: ${collectionId}`, {
        requestId,
        collectionId,
      });

      return response;
    } catch (error) {
      this.logError(
        requestId,
        'GET',
        BILLPLZ_ENDPOINTS.COLLECTION_BY_ID(collectionId),
        error,
      );
      throw error;
    }
  }

  // ================================
  // UTILITY METHODS
  // ================================

  /**
   * Check if BillPlz service is healthy
   */
  async healthCheck(): Promise<
    BillPlzApiResponse<{ status: string; timestamp: string }>
  > {
    const requestId = generateRequestId();

    try {
      this.logRequest(requestId, 'GET', '/health');

      // Try to get collections as a health check
      await this.makeRequest<unknown>('GET', BILLPLZ_ENDPOINTS.COLLECTIONS);

      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
        },
        meta: {
          requestId,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logError(requestId, 'GET', '/health', error);

      return {
        success: false,
        error: 'BillPlz service unavailable',
        meta: {
          requestId,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get current rate limit status
   */
  getRateLimitStatus(): {
    requestsInLastMinute: number;
    isCircuitOpen: boolean;
    circuitBreakerFailures: number;
  } {
    const oneMinuteAgo = Date.now() - 60000;
    const recentRequests = this.rateLimitState.requests.filter(
      (req) => req.timestamp > oneMinuteAgo,
    );

    return {
      requestsInLastMinute: recentRequests.length,
      isCircuitOpen: this.rateLimitState.isCircuitOpen,
      circuitBreakerFailures: this.rateLimitState.circuitBreakerFailures,
    };
  }

  // ================================
  // PRIVATE HELPER METHODS
  // ================================

  private async makeRequest<T>(
    method: HttpMethod,
    endpoint: string,
    options: Omit<RequestOptions, 'method'> = {},
  ): Promise<BillPlzApiResponse<T>> {
    const startTime = Date.now();
    const url = getApiEndpoint(endpoint);

    // Check circuit breaker
    await this.checkCircuitBreaker();

    // Check rate limiting
    await this.checkRateLimit();

    try {
      const response = await retryWithBackoff(
        () => this.executeRequest<T>(method, url, options),
        RATE_LIMIT_CONFIG.MAX_RETRIES,
        RATE_LIMIT_CONFIG.BASE_DELAY_MS,
        RATE_LIMIT_CONFIG.MAX_DELAY_MS,
      );

      // Record successful request
      this.recordRequest(true);

      return response;
    } catch (error) {
      // Record failed request
      this.recordRequest(false);

      // Handle specific error types
      if (error instanceof Error) {
        throw this.enhanceError(error, method, url, startTime);
      }

      throw error;
    }
  }

  private async executeRequest<T>(
    method: HttpMethod,
    url: string,
    options: Omit<RequestOptions, 'method'>,
  ): Promise<BillPlzApiResponse<T>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Basic ${Buffer.from(this.config.apiKey + ':').toString('base64')}`,
      'User-Agent': 'SimPLE-BillPlz-Client/1.0.0',
      ...options.headers,
    };

    const fetchOptions: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(options.timeout || this.config.timeout),
    };

    if (
      options.body &&
      (method === 'POST' || method === 'PUT' || method === 'PATCH')
    ) {
      fetchOptions.body = JSON.stringify(options.body);
    }

    const response = await fetch(url, fetchOptions);

    // Handle rate limiting
    if (response.status === HTTP_STATUS_CODES.TOO_MANY_REQUESTS) {
      const retryAfter = response.headers.get('Retry-After');
      throw this.createRateLimitError(
        'Rate limit exceeded',
        retryAfter ? parseInt(retryAfter) : undefined,
      );
    }

    // Parse response
    let responseData: unknown;
    const contentType = response.headers.get('content-type');

    if (contentType?.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    // Handle error responses
    if (!response.ok) {
      throw this.createApiError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response.statusText,
        responseData,
        { url, method },
      );
    }

    return {
      success: true,
      data: responseData as T,
      meta: {
        requestId: generateRequestId(),
        timestamp: new Date().toISOString(),
        rateLimitRemaining: response.headers.get('X-RateLimit-Remaining')
          ? parseInt(response.headers.get('X-RateLimit-Remaining')!)
          : undefined,
        rateLimitReset: response.headers.get('X-RateLimit-Reset') || undefined,
      },
    };
  }

  private async checkCircuitBreaker(): Promise<void> {
    const now = Date.now();

    // Check if circuit should be reset
    if (
      this.rateLimitState.isCircuitOpen &&
      now - this.rateLimitState.lastFailureTime >
        RATE_LIMIT_CONFIG.RECOVERY_TIMEOUT_MS
    ) {
      this.rateLimitState.isCircuitOpen = false;
      this.rateLimitState.circuitBreakerFailures = 0;
      this.logInfo('Circuit breaker reset');
    }

    // Throw error if circuit is open
    if (this.rateLimitState.isCircuitOpen) {
      throw this.createNetworkError(
        `Circuit breaker is open. Service temporarily unavailable. Will retry after ${new Date(
          this.rateLimitState.lastFailureTime +
            RATE_LIMIT_CONFIG.RECOVERY_TIMEOUT_MS,
        ).toISOString()}`,
      );
    }
  }

  private async checkRateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old requests
    this.rateLimitState.requests = this.rateLimitState.requests.filter(
      (req) => req.timestamp > oneMinuteAgo,
    );

    // Check rate limit
    if (
      this.rateLimitState.requests.length >=
      RATE_LIMIT_CONFIG.MAX_REQUESTS_PER_MINUTE
    ) {
      const oldestRequest = this.rateLimitState.requests[0];
      const waitTime = Math.ceil(
        (oldestRequest.timestamp + 60000 - now) / 1000,
      );

      throw this.createRateLimitError(
        `Rate limit exceeded. ${this.rateLimitState.requests.length} requests in the last minute.`,
        waitTime,
      );
    }
  }

  private recordRequest(success: boolean): void {
    const now = Date.now();

    // Record request
    this.rateLimitState.requests.push({
      timestamp: now,
      success,
    });

    // Update circuit breaker
    if (!success) {
      this.rateLimitState.circuitBreakerFailures++;
      this.rateLimitState.lastFailureTime = now;

      // Open circuit if failure threshold exceeded
      if (
        this.rateLimitState.circuitBreakerFailures >=
        RATE_LIMIT_CONFIG.FAILURE_THRESHOLD
      ) {
        this.rateLimitState.isCircuitOpen = true;
        this.logWarn(
          `Circuit breaker opened after ${this.rateLimitState.circuitBreakerFailures} failures`,
        );
      }
    } else {
      // Reset failure count on successful request
      this.rateLimitState.circuitBreakerFailures = Math.max(
        0,
        this.rateLimitState.circuitBreakerFailures - 1,
      );
    }
  }

  private validateBillCreationParams(params: {
    contractorEmail: string;
    contractorName: string;
    contractorMobile?: string;
    amount: number;
    description: string;
  }): void {
    const errors: string[] = [];

    // Email validation
    if (!params.contractorEmail || !validateEmail(params.contractorEmail)) {
      errors.push('Valid email address is required');
    }

    // Name validation
    if (
      !params.contractorName ||
      typeof params.contractorName !== 'string' ||
      params.contractorName.trim().length < 2
    ) {
      errors.push('Contractor name must be at least 2 characters');
    }

    // Mobile validation (if provided)
    if (
      params.contractorMobile &&
      !validateMalaysianMobile(params.contractorMobile)
    ) {
      errors.push('Invalid Malaysian mobile number format');
    }

    // Amount validation
    try {
      validateAmount(params.amount);
    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Invalid amount');
    }

    // Description validation
    if (
      !params.description ||
      typeof params.description !== 'string' ||
      params.description.trim().length < 5
    ) {
      errors.push('Description must be at least 5 characters');
    }

    if (errors.length > 0) {
      throw this.createValidationError(
        `Validation failed: ${errors.join(', ')}`,
      );
    }
  }

  // ================================
  // ERROR CREATION METHODS
  // ================================

  private createApiError(
    message: string,
    status: number,
    statusText: string,
    responseData?: unknown,
    request?: { url: string; method: string },
  ): BillPlzApiError {
    const error = new Error(message) as BillPlzApiError;
    error.name = 'BillPlzApiError';
    error.status = status;
    error.statusText = statusText;

    if (
      responseData &&
      typeof responseData === 'object' &&
      'error' in (responseData as object)
    ) {
      error.billplzError = responseData as BillPlzError;
    }

    if (request) {
      error.request = request;
    }

    return error;
  }

  private createNetworkError(
    message: string,
    cause?: unknown,
  ): BillPlzNetworkError {
    const error = new Error(message) as BillPlzNetworkError;
    error.name = 'BillPlzNetworkError';
    error.cause = cause;
    return error;
  }

  private createValidationError(
    message: string,
    field?: string,
    value?: unknown,
  ): BillPlzValidationError {
    const error = new Error(message) as BillPlzValidationError;
    error.name = 'BillPlzValidationError';
    error.field = field;
    error.value = value;
    return error;
  }

  private createRateLimitError(
    message: string,
    retryAfter?: number,
  ): BillPlzRateLimitError {
    const error = new Error(message) as BillPlzRateLimitError;
    error.name = 'BillPlzRateLimitError';
    error.retryAfter = retryAfter;
    return error;
  }

  private enhanceError(
    error: Error,
    _method: string,
    _url: string,
    _startTime: number,
  ): Error {
    if (error.name === 'AbortError') {
      return this.createNetworkError(
        `Request timeout after ${this.config.timeout}ms`,
        error,
      );
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return this.createNetworkError(
        'Network error: Unable to connect to BillPlz',
        error,
      );
    }

    return error;
  }

  // ================================
  // LOGGING METHODS
  // ================================

  private logRequest(
    requestId: string,
    method: string,
    endpoint: string,
    body?: unknown,
  ): void {
    if (!LOGGING_CONFIG.LOG_REQUESTS) return;

    const log: RequestLog = {
      type: 'request',
      requestId,
      method,
      url: endpoint,
      timestamp: new Date().toISOString(),
      headers: {
        Authorization: `Basic ${getMaskedApiKey()}`,
        'Content-Type': 'application/json',
      },
      body: LOGGING_CONFIG.MASK_API_KEY ? maskSensitiveData(body) : body,
    };

    console.info('BillPlz Request:', log);
  }

  private logResponse(
    requestId: string,
    method: string,
    endpoint: string,
    status: number,
    body?: unknown,
    responseTime?: number,
  ): void {
    if (!LOGGING_CONFIG.LOG_RESPONSES) return;

    const log: ResponseLog = {
      type: 'response',
      requestId,
      method,
      url: endpoint,
      timestamp: new Date().toISOString(),
      status,
      statusText: 'OK',
      responseTime: responseTime || 0,
      headers: {},
      body: body,
    };

    console.info('BillPlz Response:', log);
  }

  private logError(
    requestId: string,
    method: string,
    endpoint: string,
    error: unknown,
  ): void {
    if (!LOGGING_CONFIG.LOG_ERRORS) return;

    const log: ErrorLog = {
      type: 'error',
      requestId,
      method,
      url: endpoint,
      timestamp: new Date().toISOString(),
      error: {
        name: error instanceof Error ? error.name : 'UnknownError',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      },
    };

    console.error('BillPlz Error:', log);
  }

  private logInfo(message: string, context?: Record<string, unknown>): void {
    if (LOGGING_CONFIG.LOG_REQUESTS) {
      console.info(`BillPlz: ${message}`, context);
    }
  }

  private logWarn(message: string, context?: Record<string, unknown>): void {
    console.warn(`BillPlz: ${message}`, context);
  }
}

// ================================
// SINGLETON INSTANCE
// ================================

let clientInstance: BillPlzClient | null = null;

/**
 * Get singleton BillPlz client instance
 */
export function getBillPlzClient(): BillPlzClient {
  if (!clientInstance) {
    clientInstance = new BillPlzClient();
  }
  return clientInstance;
}

// ================================
// EXPORTS
// ================================

export default getBillPlzClient;
