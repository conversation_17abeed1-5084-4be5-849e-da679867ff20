import type {
  CreatePaymentParams,
  PaymentStatusUpdate,
} from '@/features/billing/services';
import { paymentsService } from '@/features/billing/services';
import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import type {
  PaymentRecord,
  PaymentRecordWithSubscription,
  PaymentStatus,
} from '@/types/billing';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useInvalidateProjectAccess } from './useProjectAccess';

// ================================
// PAYMENT RECORD QUERIES
// ================================

/**
 * Fetch payment history for a specific subscription with pagination
 */
export function useSubscriptionPaymentHistory(
  subscriptionId: string,
  options: {
    page?: number;
    limit?: number;
    status?: PaymentStatus;
  } = {},
) {
  const { page = 1, limit = 20 } = options;
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: [
      'subscription-payment-history',
      subscriptionId,
      page,
      limit,
      options.status,
    ],
    queryFn: async () => {
      if (!user || !subscriptionId)
        return { data: [], count: 0, hasMore: false };

      const result =
        await paymentsService.getPaymentsBySubscription(subscriptionId);

      if (result.error) {
        throw new Error(result.error);
      }

      // Apply client-side filtering and pagination for now
      let filteredPayments = result.data;

      if (options.status) {
        filteredPayments = filteredPayments.filter(
          (p) => p.status === options.status,
        );
      }

      const offset = (page - 1) * limit;
      const paginatedPayments = filteredPayments.slice(offset, offset + limit);

      return {
        data: paginatedPayments as PaymentRecordWithSubscription[],
        count: filteredPayments.length,
        hasMore: filteredPayments.length > offset + limit,
      };
    },
    enabled: !!user && !!subscriptionId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Fetch payment history for all contractor's subscriptions
 */
export function useContractorPaymentHistory(
  options: {
    page?: number;
    limit?: number;
    status?: PaymentStatus;
    projectId?: string;
  } = {},
) {
  const { page = 1, limit = 20, status, projectId } = options;
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: [
      'contractor-payment-history',
      contractorId,
      page,
      limit,
      status,
      projectId,
    ],
    queryFn: async () => {
      if (!user || !contractorId) return { data: [], count: 0, hasMore: false };

      const result =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (result.error) {
        throw new Error(result.error);
      }

      // Apply client-side filtering and pagination for now
      let filteredPayments = result.data;

      if (status) {
        filteredPayments = filteredPayments.filter((p) => p.status === status);
      }

      if (projectId) {
        filteredPayments = filteredPayments.filter(
          (p) => p.subscription?.project_id === projectId,
        );
      }

      const offset = (page - 1) * limit;
      const paginatedPayments = filteredPayments.slice(offset, offset + limit);

      return {
        data: paginatedPayments,
        count: filteredPayments.length,
        hasMore: filteredPayments.length > offset + limit,
      };
    },
    enabled: !!user && !!contractorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Fetch payment details by ID with subscription context
 */
export function usePaymentDetails(paymentId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['payment-details', paymentId, user?.id],
    queryFn: async (): Promise<PaymentRecordWithSubscription | null> => {
      if (!user || !paymentId) return null;

      const result =
        await paymentsService.getPaymentWithSubscription(paymentId);

      if (result.error) {
        throw new Error(result.error);
      }

      return result.data;
    },
    enabled: !!user && !!paymentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get failed payments that can be retried
 */
export function useRetriablePayments() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['retriable-payments', contractorId],
    queryFn: async (): Promise<PaymentRecordWithSubscription[]> => {
      if (!user || !contractorId) return [];

      const result =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (result.error) {
        throw new Error(result.error);
      }

      // Filter for failed payments that can be retried
      return result.data.filter((payment) => payment.status === 'failed');
    },
    enabled: !!user && !!contractorId,
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Get payment statistics for contractor
 */
export function usePaymentStats() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['payment-stats', contractorId],
    queryFn: async () => {
      if (!user || !contractorId) {
        return {
          totalPayments: 0,
          successfulPayments: 0,
          failedPayments: 0,
          pendingPayments: 0,
          totalAmountPaid: 0,
          successRate: 0,
        };
      }

      const result =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (result.error) {
        throw new Error(result.error);
      }

      const payments = result.data;
      const stats = {
        totalPayments: payments.length,
        successfulPayments: 0,
        failedPayments: 0,
        pendingPayments: 0,
        totalAmountPaid: 0,
        successRate: 0,
      };

      payments.forEach((payment) => {
        switch (payment.status) {
          case 'paid':
            stats.successfulPayments++;
            stats.totalAmountPaid += payment.amount || 0;
            break;
          case 'failed':
            stats.failedPayments++;
            break;
          case 'pending':
            stats.pendingPayments++;
            break;
        }
      });

      if (stats.totalPayments > 0) {
        stats.successRate = Math.round(
          (stats.successfulPayments / stats.totalPayments) * 100,
        );
      }

      return stats;
    },
    enabled: !!user && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// ================================
// PAYMENT RECORD MUTATIONS
// ================================

/**
 * Create a new payment record
 */
export function useCreatePaymentRecord() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      paymentData: CreatePaymentParams,
    ): Promise<PaymentRecord> => {
      const result = await paymentsService.createPaymentRecord(paymentData);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to create payment record');
      }

      return result.data;
    },
    onSuccess: (_data) => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['payment-stats', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['retriable-payments', contractorId],
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Payment Record',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Update payment record status
 */
export function useUpdatePaymentRecord() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      updateParams: PaymentStatusUpdate,
    ): Promise<PaymentRecord> => {
      const result = await paymentsService.updatePaymentStatus(updateParams);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to update payment record');
      }

      return result.data.paymentRecord;
    },
    onSuccess: (data) => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['payment-details', data.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['payment-stats', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['retriable-payments', contractorId],
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Update Payment Record',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Retry failed payment with access restoration
 */
export function useRetryFailedPayment() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      paymentId,
      projectId: _projectId, // Used in onSuccess callback
    }: {
      paymentId: string;
      projectId: string;
    }): Promise<{ paymentRecord: PaymentRecord; billUrl?: string }> => {
      const result = await paymentsService.retryFailedPayment(paymentId);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to retry payment');
      }

      return {
        paymentRecord: result.data.paymentRecord,
        billUrl: result.data.billUrl,
      };
    },
    onSuccess: (data, variables) => {
      const { projectId } = variables;
      const contractorId = user?.profile?.contractor_id;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['payment-details', variables.paymentId],
      });
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['payment-stats', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['retriable-payments', contractorId],
      });

      // Invalidate project access - if payment succeeds, access might be restored
      invalidateProjectAccess(projectId, contractorId || undefined, user?.id);

      toast({
        title: 'Payment Retry Initiated',
        description:
          'A new payment attempt has been created. Complete the payment to restore access.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Retry Payment',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

// ================================
// UTILITY HOOKS
// ================================

/**
 * Get recent payments for dashboard display
 */
export function useRecentPayments(limit = 5) {
  const { data: paymentHistory } = useContractorPaymentHistory({
    page: 1,
    limit,
  });

  return {
    payments: paymentHistory?.data || [],
    isLoading: !paymentHistory,
  };
}

/**
 * Get overdue payments requiring immediate attention
 */
export function useOverduePayments() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['overdue-payments', contractorId],
    queryFn: async (): Promise<PaymentRecordWithSubscription[]> => {
      if (!user || !contractorId) return [];

      const result =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (result.error) {
        throw new Error(result.error);
      }

      // Get failed payments older than 3 days
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      return result.data
        .filter(
          (payment) =>
            payment.status === 'failed' &&
            new Date(payment.created_at) < threeDaysAgo,
        )
        .sort(
          (a, b) =>
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
        );
    },
    enabled: !!user && !!contractorId,
    staleTime: 60 * 1000, // 1 minute
  });
}
