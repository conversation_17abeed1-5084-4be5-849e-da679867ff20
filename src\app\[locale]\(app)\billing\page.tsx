'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ContractorProjectOverview } from '@/features/billing/components/ContractorProjectOverview';
import { usePmaSubscriptions } from '@/features/billing/hooks/usePmaSubscriptions';
import {
  groupSubscriptionsByProject,
  calculateBillingStats,
} from '@/features/billing/utils/project-grouping';
import { useUserWithProfile } from '@/hooks/use-auth';
import { AlertTriangle, RefreshCw, Shield, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * Main billing dashboard with access state overview
 * RBAC middleware protection (contractors only)
 * Internationalization setup with access control terms
 * Project access summary grid
 * Subscription health indicators
 */
export default function BillingPage() {
  const t = useTranslations('billing');
  const router = useRouter();
  const { data: user } = useUserWithProfile();

  const [refreshKey, setRefreshKey] = useState(0);

  // RBAC Protection - contractors only
  useEffect(() => {
    if (user && user.profile?.user_role !== 'contractor') {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Data fetching - PMA-based subscriptions
  const contractorId = user?.profile?.contractor_id;

  const {
    subscriptions,
    isLoading: subscriptionsLoading,
    error: subscriptionsError,
    refetch: refetchSubscriptions,
  } = usePmaSubscriptions({
    contractorId: contractorId || undefined,
  });

  const isLoading = subscriptionsLoading;

  // Group subscriptions by project and calculate stats
  const contractorData = groupSubscriptionsByProject(subscriptions);
  const stats = calculateBillingStats(contractorData);

  const hasUrgentItems = contractorData.has_urgent_payments;

  // Handle access control
  if (!user) {
    return <BillingPageSkeleton />;
  }

  if (user.profile?.user_role !== 'contractor') {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <CardTitle>Access Restricted</CardTitle>
            <CardDescription>
              Only contractors can access billing information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => router.push('/dashboard')}
              className="w-full"
            >
              Return to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error handling
  if (subscriptionsError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('dashboard.title')}
            </h1>
            <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('dashboard.errors.loadFailed')}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                refetchSubscriptions();
                setRefreshKey((prev) => prev + 1);
              }}
              className="ml-2"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              {t('dashboard.errors.retry')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Handlers
  const handleCreatePayment = (subscriptionId: string) => {
    router.push(`/billing/subscription/${subscriptionId}`);
  };

  const handleViewPma = (pmaId: string) => {
    router.push(`/pmas/${pmaId}`);
  };

  const handleManageSubscription = (subscriptionId: string) => {
    router.push(`/billing/subscription/${subscriptionId}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {t('dashboard.title')}
        </h1>
        <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
      </div>

      {/* Urgent Actions Banner */}
      {hasUrgentItems && (
        <Alert
          variant="destructive"
          className="border-red-200 bg-red-50 dark:bg-red-950"
        >
          <Zap className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  {t('dashboard.urgentActions.title')}
                </p>
                <p className="text-sm">
                  {stats.urgent_payments_count}{' '}
                  {stats.urgent_payments_count === 1
                    ? 'PMA subscription requires'
                    : 'PMA subscriptions require'}{' '}
                  immediate attention
                </p>
              </div>
              <Button
                size="sm"
                className="bg-red-600 hover:bg-red-700"
                onClick={() => router.push('/billing/suspended')}
              >
                {t('dashboard.urgentActions.viewDetails')}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Contractor Project Overview */}
      <ContractorProjectOverview
        key={refreshKey}
        contractorData={contractorData}
        isLoading={isLoading}
        onPayNow={handleCreatePayment}
        onViewProject={handleViewPma}
        onManageSubscription={handleManageSubscription}
        onCancel={(subscriptionId) => console.log('Cancel:', subscriptionId)}
        onReactivate={(subscriptionId) =>
          console.log('Reactivate:', subscriptionId)
        }
        onUpdatePaymentMethod={(subscriptionId) =>
          console.log('Update payment:', subscriptionId)
        }
        onViewPaymentHistory={(subscriptionId) =>
          console.log('View history:', subscriptionId)
        }
        onUpdateBilling={(subscriptionId) =>
          console.log('Update billing:', subscriptionId)
        }
        onRefresh={() => {
          refetchSubscriptions();
          setRefreshKey((prev) => prev + 1);
        }}
      />
    </div>
  );
}

/**
 * Loading skeleton for the billing page
 */
function BillingPageSkeleton() {
  return (
    <div className="space-y-6">
      <div>
        <Skeleton className="h-9 w-64 mb-2" />
        <Skeleton className="h-5 w-96" />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
