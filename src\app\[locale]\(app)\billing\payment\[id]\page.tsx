'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import {
  usePaymentDetails,
  useRetryFailedPayment,
} from '@/features/billing/hooks';
import { convertCentsToMyr } from '@/types/billing';
import { format } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle,
  Clock,
  CreditCard,
  Download,
  ExternalLink,
  Mail,
  Printer,
  RefreshCw,
  Shield,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * Payment confirmation with access restoration context
 * Receipt display and download with access notes
 * Payment retry for failed payments
 * Access restoration confirmation
 */
export default function PaymentConfirmationPage() {
  const t = useTranslations('billing.payment');
  const tCommon = useTranslations('common');
  const router = useRouter();
  const params = useParams();
  const paymentId = params.id as string;

  const [accessCheckComplete, setAccessCheckComplete] = useState(false);

  // Data fetching
  const {
    data: payment,
    isLoading: paymentLoading,
    error: paymentError,
    refetch: refetchPayment,
  } = usePaymentDetails(paymentId);

  const retryPaymentMutation = useRetryFailedPayment();

  const isLoading = paymentLoading;
  const paymentAmount = payment ? convertCentsToMyr(payment.amount) : 0;

  // Simulate access restoration check
  useEffect(() => {
    if (payment?.status === 'paid') {
      const timer = setTimeout(() => {
        setAccessCheckComplete(true);
      }, 3000); // 3 second delay to simulate processing
      return () => clearTimeout(timer);
    }
  }, [payment?.status]);

  // Handlers
  const handleBack = () => {
    router.push('/billing');
  };

  const handleViewProject = () => {
    if (payment?.subscription?.project_id) {
      router.push(`/projects/${payment.subscription.project_id}`);
    }
  };

  const handleRetryPayment = async () => {
    if (!payment?.subscription?.project_id) return;

    try {
      await retryPaymentMutation.mutateAsync({
        paymentId: payment.id,
        projectId: payment.subscription.project_id,
      });
      refetchPayment();
    } catch (error) {
      console.error('Failed to retry payment:', error);
    }
  };

  const handleContactSupport = () => {
    // Navigate to support page or open support chat
    window.open('mailto:<EMAIL>', '_blank');
  };

  const handlePrintReceipt = () => {
    window.print();
  };

  const handleEmailReceipt = () => {
    // Implement email receipt functionality
    alert('Receipt email feature coming soon');
  };

  // Loading state
  if (isLoading) {
    return <PaymentPageSkeleton />;
  }

  // Error state
  if (paymentError || !payment) {
    return (
      <div className="max-w-2xl mx-auto py-8">
        <Card>
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <CardTitle>Payment Not Found</CardTitle>
            <CardDescription>
              The requested payment could not be found or you don&apos;t have
              access to it.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Billing
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusIcon = () => {
    switch (payment.status) {
      case 'paid':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'failed':
        return <X className="h-6 w-6 text-red-600" />;
      case 'pending':
        return <Clock className="h-6 w-6 text-yellow-600" />;
      default:
        return <Clock className="h-6 w-6 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (payment.status) {
      case 'paid':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack} size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground">
              Payment confirmation and receipt details
            </p>
          </div>
        </div>
        <Button variant="outline" onClick={() => refetchPayment()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          {tCommon('refresh')}
        </Button>
      </div>

      {/* Status Banner */}
      <div className="mb-6">
        {payment.status === 'paid' && (
          <Alert className="border-green-200 bg-green-50 dark:bg-green-950">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-green-800">
                    {t('status.completed')}
                  </p>
                  <p className="text-sm text-green-700">
                    Payment processed successfully and access has been restored.
                  </p>
                </div>
                <Button
                  onClick={handleViewProject}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {t('actions.viewProject')}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {payment.status === 'failed' && (
          <Alert variant="destructive">
            <X className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{t('status.failed')}</p>
                  <p className="text-sm">{t('retry.description')}</p>
                </div>
                <Button
                  onClick={handleRetryPayment}
                  disabled={retryPaymentMutation.isPending}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {retryPaymentMutation.isPending
                    ? 'Retrying...'
                    : t('actions.retryPayment')}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {payment.status === 'pending' && (
          <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950">
            <Clock className="h-4 w-4 text-yellow-600" />
            <AlertDescription>
              <p className="font-medium text-yellow-800">
                {t('status.pending')}
              </p>
              <p className="text-sm text-yellow-700">
                Your payment is being processed. This may take a few minutes.
              </p>
            </AlertDescription>
          </Alert>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Payment Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t('details.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between py-2">
              <span className="text-sm font-medium text-muted-foreground">
                Status
              </span>
              <div className="flex items-center gap-2">
                {getStatusIcon()}
                <span className={`font-medium ${getStatusColor()}`}>
                  {t(`status.${payment.status}`)}
                </span>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  {t('details.amount')}
                </span>
                <span className="text-lg font-bold">
                  RM {paymentAmount.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  {t('details.date')}
                </span>
                <span className="text-sm">
                  {payment.created_at
                    ? format(new Date(payment.created_at), 'MMM dd, yyyy HH:mm')
                    : 'Unknown'}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  {t('details.method')}
                </span>
                <span className="text-sm">BillPlz</span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  {t('details.reference')}
                </span>
                <span className="text-sm font-mono">
                  {payment.billplz_bill_id || payment.id.slice(0, 8)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  {t('details.description')}
                </span>
                <span className="text-sm">Monthly subscription payment</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Access Restoration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t('accessRestoration.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {payment.status === 'paid' ? (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-green-100 text-green-600">
                    {accessCheckComplete ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Clock className="h-4 w-4 animate-spin" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium">
                      {accessCheckComplete
                        ? t('accessRestoration.restored')
                        : t('accessRestoration.processing')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {accessCheckComplete
                        ? 'Full project access has been activated'
                        : 'Please wait while we restore your access...'}
                    </p>
                  </div>
                </div>

                {accessCheckComplete && (
                  <div className="pt-4">
                    <Button onClick={handleViewProject} className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      {t('actions.viewProject')}
                    </Button>
                  </div>
                )}
              </div>
            ) : payment.status === 'failed' ? (
              <div className="text-center py-4">
                <X className="h-8 w-8 mx-auto text-red-500 mb-2" />
                <p className="text-red-600 font-medium">
                  {t('accessRestoration.failed')}
                </p>
                <p className="text-sm text-muted-foreground">
                  Payment failed - access remains restricted
                </p>
              </div>
            ) : (
              <div className="text-center py-4">
                <Clock className="h-8 w-8 mx-auto text-yellow-500 mb-2 animate-pulse" />
                <p className="text-yellow-600 font-medium">
                  Awaiting Payment Confirmation
                </p>
                <p className="text-sm text-muted-foreground">
                  Access will be restored once payment is confirmed
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Receipt Actions */}
      {payment.status === 'paid' && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              {t('receipt.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <Button onClick={() => {}} className="flex-1">
                <Download className="h-4 w-4 mr-2" />
                {t('receipt.download')}
              </Button>
              <Button
                variant="outline"
                onClick={handleEmailReceipt}
                className="flex-1"
              >
                <Mail className="h-4 w-4 mr-2" />
                {t('receipt.email')}
              </Button>
              <Button
                variant="outline"
                onClick={handlePrintReceipt}
                className="flex-1"
              >
                <Printer className="h-4 w-4 mr-2" />
                {t('receipt.print')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3 mt-6">
        <Button onClick={handleBack} variant="outline" className="flex-1">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('actions.backToBilling')}
        </Button>

        {payment.status === 'failed' && (
          <Button
            onClick={handleRetryPayment}
            disabled={retryPaymentMutation.isPending}
            className="flex-1"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {retryPaymentMutation.isPending
              ? 'Retrying...'
              : t('retry.newPayment')}
          </Button>
        )}

        {payment.status !== 'paid' && (
          <Button
            onClick={handleContactSupport}
            variant="outline"
            className="flex-1"
          >
            {t('actions.contactSupport')}
          </Button>
        )}

        {payment.status === 'paid' && accessCheckComplete && (
          <Button onClick={handleViewProject} className="flex-1">
            <ExternalLink className="h-4 w-4 mr-2" />
            {t('actions.viewProject')}
          </Button>
        )}
      </div>
    </div>
  );
}

/**
 * Loading skeleton for payment page
 */
function PaymentPageSkeleton() {
  return (
    <div className="max-w-4xl mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-16" />
          <div>
            <Skeleton className="h-9 w-64 mb-2" />
            <Skeleton className="h-5 w-96" />
          </div>
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      <Skeleton className="h-20 w-full mb-6" />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3 mt-6">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 flex-1" />
      </div>
    </div>
  );
}
