import { pmaSubscriptionsService } from '@/features/billing/services/pma-subscriptions.service';
import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createPMACertificate } from '../services/create-pma-certificate';
import type {
  CreatePMACertificateInput,
  CreatePMACertificateResponse,
} from '../types/create-pma';
import {
  PMA_CERTIFICATES_QUERY_KEY,
  PMA_STATS_QUERY_KEY,
} from './use-pma-certificates';

export function useCreatePMACertificate(options?: {
  onSuccess?: (data: CreatePMACertificateResponse) => void;
  autoCreateSubscription?: boolean;
}) {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useMutation<
    CreatePMACertificateResponse,
    Error,
    CreatePMACertificateInput
  >({
    mutationFn: async (data) => {
      // Debug: Log the input data
      console.log('Creating PMA certificate, input data:', {
        ...data,
        file_url: data.file_url ? '[FILE URL]' : null, // Mask the full URL
      });

      try {
        const result = await createPMACertificate(data);
        console.log('PMA certificate created successfully:', {
          id: result.id,
          pma_number: result.pma_number,
          project_id: result.project_id,
        });
        return result;
      } catch (error) {
        console.error('Failed to create PMA certificate:', error);
        throw error;
      }
    },
    onSuccess: async (data) => {
      // Invalidate any existing queries for PMA certificates for this project
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY, data.project_id],
      });

      // Also invalidate PMA stats to ensure counts are updated
      queryClient.invalidateQueries({
        queryKey: [PMA_STATS_QUERY_KEY, data.project_id],
      });

      // Auto-create PMA subscription (default behavior)
      const shouldAutoCreate = options?.autoCreateSubscription !== false; // Default to true

      if (shouldAutoCreate && contractorId) {
        try {
          const subscriptionResult = await pmaSubscriptionsService.create({
            pmaId: data.id,
            contractorId,
            status: 'pending_payment',
          });

          if (subscriptionResult.error) {
            console.error(
              'Failed to auto-create PMA subscription:',
              subscriptionResult.error,
            );
            toast({
              title: 'PMA Certificate Created',
              description: `Certificate created but failed to create subscription: ${subscriptionResult.error}. Please create manually.`,
              variant: 'destructive',
            });
          } else {
            // Invalidate subscription-related queries
            queryClient.invalidateQueries({
              queryKey: ['pma-subscriptions', contractorId],
            });
            queryClient.invalidateQueries({
              queryKey: ['pma-access', data.project_id],
            });

            toast({
              title: 'PMA Certificate & Subscription Created',
              description:
                'PMA certificate and billing subscription created successfully. Complete payment to activate access.',
            });
          }
        } catch (error) {
          console.error('Error auto-creating PMA subscription:', error);
          toast({
            title: 'PMA Certificate Created',
            description: `Certificate created but failed to create subscription: ${error instanceof Error ? error.message : 'Unknown error'}. Please create manually.`,
            variant: 'destructive',
          });
        }
      }

      // Call custom onSuccess if provided
      if (options?.onSuccess) {
        options.onSuccess(data);
      }
    },
    // Add mutation key to fix TanStack Query error
    mutationKey: ['createPMACertificate'],
  });
}
