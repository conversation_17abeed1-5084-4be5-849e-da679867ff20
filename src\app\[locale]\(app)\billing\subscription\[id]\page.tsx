'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { GracePeriodWarning } from '@/features/billing/components/GracePeriodWarning';
import {
  useCancelSubscription,
  useProjectSubscription,
  useReactivateSubscription,
  useSubscriptionPaymentHistory,
} from '@/features/billing/hooks';
import {
  convertCentsToMyr,
  getDaysRemainingInGracePeriod,
} from '@/types/billing';
import { format } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle,
  CreditCard,
  Download,
  ExternalLink,
  RefreshCw,
  Shield,
  X,
  Zap,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

/**
 * Individual subscription management with access control
 * Payment history with access impact timeline
 * Project access status detailed view
 * Edit/cancel subscription with access implications
 * Grace period management interface
 */
export default function SubscriptionManagementPage() {
  const t = useTranslations('billing.subscription');
  const tCommon = useTranslations('common');
  const router = useRouter();
  const params = useParams();
  const subscriptionId = params.id as string;

  const [showCancelDialog, setShowCancelDialog] = useState(false);

  // Data fetching
  const {
    data: subscription,
    isLoading: subscriptionLoading,
    error: subscriptionError,
    refetch: refetchSubscription,
  } = useProjectSubscription(subscriptionId);

  const { data: paymentHistory, isLoading: historyLoading } =
    useSubscriptionPaymentHistory(subscriptionId);

  const cancelMutation = useCancelSubscription();
  const reactivateMutation = useReactivateSubscription();

  const isLoading = subscriptionLoading;
  const monthlyAmount = subscription
    ? convertCentsToMyr(subscription.amount || 0)
    : 0;
  const gracePeriodDays = subscription?.grace_period_ends
    ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
    : null;

  // Handlers
  const handleBack = () => {
    router.push('/billing');
  };

  const handlePayNow = () => {
    if (subscription?.project_id) {
      router.push(`/billing/setup/${subscription.project_id}`);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    try {
      await cancelMutation.mutateAsync({
        subscriptionId: subscription.id,
        projectId: subscription.project_id || undefined,
      });
      setShowCancelDialog(false);
      refetchSubscription();
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
    }
  };

  const handleReactivateSubscription = async () => {
    if (!subscription) return;

    try {
      await reactivateMutation.mutateAsync({
        subscriptionId: subscription.id,
        projectId: subscription.project_id || undefined,
      });
      refetchSubscription();
    } catch (error) {
      console.error('Failed to reactivate subscription:', error);
    }
  };

  const handleViewProject = () => {
    if (subscription?.project_id) {
      router.push(`/projects/${subscription.project_id}`);
    }
  };

  const handleRetryPayment = () => {
    handlePayNow();
  };

  // Loading state
  if (isLoading) {
    return <SubscriptionPageSkeleton />;
  }

  // Error state
  if (subscriptionError || !subscription) {
    return (
      <div className="max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <CardTitle>Subscription Not Found</CardTitle>
            <CardDescription>
              The requested subscription could not be found or you don&apos;t
              have access to it.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Billing
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack} size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground">Subscription Management</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchSubscription()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {tCommon('refresh')}
          </Button>
          {subscription.project_id && (
            <Button onClick={handleViewProject}>
              <ExternalLink className="h-4 w-4 mr-2" />
              {t('actions.viewProject')}
            </Button>
          )}
        </div>
      </div>

      {/* Grace Period Warning */}
      {subscription.isInGracePeriod && (
        <div className="mb-6">
          <GracePeriodWarning
            subscription={subscription}
            projectName="Project"
            onPayNow={handlePayNow}
            variant="banner"
            showDismiss={false}
          />
        </div>
      )}

      {/* Main Content */}
      <div className="space-y-6">
        {/* Overview Section */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Subscription Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                {t('details.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('details.status')}
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant={
                        subscription.status === 'active'
                          ? 'default'
                          : subscription.status === 'grace_period'
                            ? 'secondary'
                            : 'destructive'
                      }
                    >
                      {t(`status.${subscription.status}`)}
                    </Badge>
                    {subscription.isInGracePeriod && (
                      <Badge variant="outline" className="text-orange-600">
                        {gracePeriodDays !== null
                          ? `${gracePeriodDays}d left`
                          : 'Grace Period'}
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('details.monthlyFee')}
                  </label>
                  <p className="text-lg font-semibold mt-1">
                    RM {monthlyAmount.toFixed(2)}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('details.nextPayment')}
                  </label>
                  <p className="text-sm mt-1">
                    {subscription.next_billing_date
                      ? format(
                          new Date(subscription.next_billing_date),
                          'MMM dd, yyyy',
                        )
                      : 'Not scheduled'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('details.created')}
                  </label>
                  <p className="text-sm mt-1">
                    {subscription.created_at
                      ? format(
                          new Date(subscription.created_at),
                          'MMM dd, yyyy',
                        )
                      : 'Unknown'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {(subscription.status === 'grace_period' ||
                subscription.status === 'suspended') && (
                <Button onClick={handlePayNow} className="w-full" size="lg">
                  <Zap className="h-4 w-4 mr-2" />
                  {t('actions.payNow')}
                </Button>
              )}

              {subscription.status === 'cancelled' && (
                <Button
                  onClick={handleReactivateSubscription}
                  className="w-full"
                  size="lg"
                  disabled={reactivateMutation.isPending}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {reactivateMutation.isPending
                    ? 'Reactivating...'
                    : t('actions.reactivate')}
                </Button>
              )}

              {subscription.status === 'active' && (
                <Button
                  variant="destructive"
                  onClick={() => setShowCancelDialog(true)}
                  className="w-full"
                >
                  <X className="h-4 w-4 mr-2" />
                  {t('actions.cancelSubscription')}
                </Button>
              )}

              <Button
                variant="outline"
                onClick={handleRetryPayment}
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {t('actions.retryPayment')}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Access Status Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t('accessStatus.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div
                    className={`p-2 rounded-lg ${
                      subscription.accessAllowed
                        ? 'bg-green-100 text-green-600'
                        : 'bg-red-100 text-red-600'
                    }`}
                  >
                    <Shield className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="font-medium">
                      {subscription.accessAllowed
                        ? t('accessStatus.fullAccess')
                        : t('accessStatus.noAccess')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {subscription.isInGracePeriod
                        ? t('accessStatus.gracePeriod')
                        : 'Project access status'}
                    </p>
                  </div>
                </div>
                <Badge
                  variant={
                    subscription.accessAllowed ? 'default' : 'destructive'
                  }
                >
                  {subscription.accessAllowed ? 'Active' : 'Restricted'}
                </Badge>
              </div>

              {!subscription.accessAllowed && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Project access is currently restricted due to billing
                    issues. Please make a payment to restore full access.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment History Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t('paymentHistory.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {historyLoading ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : paymentHistory &&
              paymentHistory.data &&
              paymentHistory.data.length > 0 ? (
              <div className="space-y-4">
                {paymentHistory.data.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`p-2 rounded-lg ${
                          payment.status === 'paid'
                            ? 'bg-green-100 text-green-600'
                            : payment.status === 'failed'
                              ? 'bg-red-100 text-red-600'
                              : 'bg-yellow-100 text-yellow-600'
                        }`}
                      >
                        <CreditCard className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">
                          RM {convertCentsToMyr(payment.amount).toFixed(2)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {payment.created_at
                            ? format(
                                new Date(payment.created_at),
                                'MMM dd, yyyy HH:mm',
                              )
                            : 'Unknown date'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          payment.status === 'paid'
                            ? 'default'
                            : payment.status === 'failed'
                              ? 'destructive'
                              : 'secondary'
                        }
                      >
                        {payment.status}
                      </Badge>
                      {payment.status === 'paid' && (
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4 mr-1" />
                          {t('paymentHistory.receipt')}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  {t('paymentHistory.noPayments')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cancellation Dialog */}
      {showCancelDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                {t('cancellation.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p>{t('cancellation.warning')}</p>
                <p className="text-sm text-muted-foreground">
                  {t('cancellation.consequences')}
                </p>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="destructive"
                  onClick={handleCancelSubscription}
                  disabled={cancelMutation.isPending}
                  className="flex-1"
                >
                  {cancelMutation.isPending
                    ? 'Cancelling...'
                    : t('cancellation.confirm')}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCancelDialog(false)}
                  className="flex-1"
                >
                  {t('cancellation.keep')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

/**
 * Loading skeleton for subscription page
 */
function SubscriptionPageSkeleton() {
  return (
    <div className="max-w-6xl mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-16" />
          <div>
            <Skeleton className="h-9 w-64 mb-2" />
            <Skeleton className="h-5 w-96" />
          </div>
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="grid grid-cols-2 gap-4">
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-32" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-32" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
