'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import {
  AlertTriangle,
  Calendar,
  Check,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  CreditCard,
  DollarSign,
  FileText,
  Shield,
} from 'lucide-react';
import { useState } from 'react';
import { PaymentForm } from './PaymentForm';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface PaymentSetupWizardProps {
  subscription: SubscriptionWithAccess;
  projectName: string;
  projectDescription?: string;
  onComplete?: (result: { success: boolean; paymentUrl?: string }) => void;
  onCancel?: () => void;
  className?: string;
  initialStep?: number;
}

type WizardStep = 'summary' | 'payment' | 'confirmation';

/**
 * Multi-step payment setup wizard for new projects with progress tracking
 */
export function PaymentSetupWizard({
  subscription,
  projectName,
  projectDescription,
  onComplete,
  onCancel,
  className,
  initialStep = 0,
}: PaymentSetupWizardProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [paymentResult, setPaymentResult] = useState<{
    success: boolean;
    paymentUrl?: string;
    error?: string;
  } | null>(null);

  const monthlyAmount = subscription.amount || 0;

  const steps: Array<{
    key: WizardStep;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
  }> = [
    {
      key: 'summary',
      title: 'Project Summary',
      description: 'Review project details and billing information',
      icon: FileText,
    },
    {
      key: 'payment',
      title: 'Payment Setup',
      description: 'Configure your billing information',
      icon: CreditCard,
    },
    {
      key: 'confirmation',
      title: 'Confirmation',
      description: 'Complete setup and activate access',
      icon: CheckCircle,
    },
  ];

  const currentStepData = steps[currentStep];
  const progressPercentage = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePaymentSubmit = async (_data: Record<string, unknown>) => {
    try {
      // This would integrate with your payment service
      const result = await new Promise<{
        success: boolean;
        paymentUrl?: string;
      }>((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            paymentUrl: `https://billplz.com/bills/example-${Date.now()}`,
          });
        }, 2000);
      });

      setPaymentResult(result);

      if (result.success) {
        setCurrentStep(2); // Move to confirmation step
      }

      return result;
    } catch (error) {
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Payment setup failed',
      };
      setPaymentResult(errorResult);
      return errorResult;
    }
  };

  const handleComplete = () => {
    onComplete?.(paymentResult || { success: false });
  };

  return (
    <div className={cn('max-w-2xl mx-auto space-y-6', className)}>
      {/* Progress Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <CardTitle className="text-xl">Payment Setup</CardTitle>
              <CardDescription>
                Set up billing for {projectName}
              </CardDescription>
            </div>
            <ProjectAccessIndicator
              status={subscription.status}
              gracePeriodEnds={subscription.grace_period_ends}
              size="sm"
            />
          </div>

          {/* Progress Bar */}
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">
                Step {currentStep + 1} of {steps.length}
              </span>
              <span className="font-medium">
                {Math.round(progressPercentage)}% Complete
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Step Navigation */}
          <div className="flex justify-between mt-4">
            {steps.map((step, index) => {
              const IconComponent = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <div key={step.key} className="flex items-center">
                  <div
                    className={cn(
                      'flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors',
                      isActive &&
                        'border-primary bg-primary text-primary-foreground',
                      isCompleted && 'border-green-500 bg-green-500 text-white',
                      !isActive &&
                        !isCompleted &&
                        'border-muted-foreground text-muted-foreground',
                    )}
                  >
                    {isCompleted ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <IconComponent className="h-4 w-4" />
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={cn(
                        'w-12 h-0.5 mx-2',
                        isCompleted ? 'bg-green-500' : 'bg-muted',
                      )}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <currentStepData.icon className="h-5 w-5" />
            {currentStepData.title}
          </CardTitle>
          <CardDescription>{currentStepData.description}</CardDescription>
        </CardHeader>

        <CardContent>
          {/* Step 1: Project Summary */}
          {currentStep === 0 && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Project Details */}
                <Card className="bg-muted/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Project Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Project Name
                      </p>
                      <p className="font-medium">{projectName}</p>
                    </div>
                    {projectDescription && (
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Description
                        </p>
                        <p className="text-sm">{projectDescription}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <Badge variant="outline" className="text-xs">
                        {subscription.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Billing Information */}
                <Card className="bg-muted/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">
                      Billing Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Monthly Subscription
                        </p>
                        <p className="font-bold text-lg">
                          RM {monthlyAmount.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Billing Cycle
                        </p>
                        <p className="font-medium">Monthly</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Payment Method
                        </p>
                        <p className="font-medium">BillPlz Gateway</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Important Information */}
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Before proceeding:</p>
                    <ul className="text-sm space-y-1 ml-4 list-disc">
                      <li>
                        Your subscription will be activated immediately after
                        payment
                      </li>
                      <li>Monthly billing will occur automatically</li>
                      <li>
                        You can cancel or modify your subscription anytime
                      </li>
                      <li>
                        All payments are processed securely through BillPlz
                      </li>
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Step 2: Payment Setup */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                <Shield className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">Secure Payment Setup</p>
                  <p className="text-sm">
                    Complete your billing information to activate project
                    access. All data is transmitted securely and processed
                    through BillPlz.
                  </p>
                </AlertDescription>
              </Alert>

              <PaymentForm
                subscription={subscription}
                projectName={projectName}
                onSubmit={handlePaymentSubmit}
                className="border-0 shadow-none bg-transparent"
              />
            </div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === 2 && (
            <div className="space-y-6 text-center">
              {paymentResult?.success ? (
                <>
                  <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
                      Payment Setup Complete!
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Your billing has been configured successfully for{' '}
                      {projectName}
                    </p>
                  </div>

                  <Card className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span>Project</span>
                          <span className="font-medium">{projectName}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Monthly Amount</span>
                          <span className="font-medium">
                            RM {monthlyAmount.toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Status</span>
                          <Badge
                            variant="outline"
                            className="text-green-700 border-green-300"
                          >
                            Ready for Payment
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {paymentResult.paymentUrl && (
                    <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                      <CreditCard className="h-4 w-4 text-blue-600" />
                      <AlertDescription>
                        <p className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                          Complete Your Payment
                        </p>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                          Click the button below to proceed to the secure
                          BillPlz payment page
                        </p>
                        <Button
                          onClick={() =>
                            window.open(paymentResult.paymentUrl, '_blank')
                          }
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Complete Payment
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </AlertDescription>
                    </Alert>
                  )}
                </>
              ) : (
                <>
                  <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">
                      Setup Failed
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {paymentResult?.error ||
                        'Unable to complete payment setup'}
                    </p>
                  </div>

                  <Button onClick={() => setCurrentStep(1)} variant="outline">
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </>
              )}
            </div>
          )}
        </CardContent>

        {/* Navigation Footer */}
        <CardContent className="border-t pt-4">
          <div className="flex justify-between">
            <div>
              {currentStep > 0 && currentStep < 2 && (
                <Button variant="outline" onClick={handlePrevious}>
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
              {onCancel && currentStep !== 2 && (
                <Button variant="ghost" onClick={onCancel} className="ml-2">
                  Cancel
                </Button>
              )}
            </div>

            <div>
              {currentStep === 0 && (
                <Button onClick={handleNext}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              )}
              {currentStep === 2 && (
                <Button onClick={handleComplete}>
                  Complete Setup
                  <Check className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
