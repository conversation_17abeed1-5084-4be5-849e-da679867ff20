'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import {
  AlertTriangle,
  ChevronRight,
  Clock,
  CreditCard,
  X,
  Zap,
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

export interface GracePeriodWarningProps {
  subscription: SubscriptionWithAccess;
  projectName: string;
  onPayNow?: () => void;
  onDismiss?: () => void;
  className?: string;
  variant?: 'banner' | 'card' | 'alert';
  showDismiss?: boolean;
  autoRefresh?: boolean;
}

/**
 * Urgent warning component for grace_period status with countdown timer
 */
export function GracePeriodWarning({
  subscription,
  projectName,
  onPayNow,
  onDismiss,
  className,
  variant = 'banner',
  showDismiss = false,
  autoRefresh = true,
}: GracePeriodWarningProps) {
  const [isDismissed, setIsDismissed] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<{
    days: number;
    hours: number;
    minutes: number;
  } | null>(null);

  const monthlyAmount = subscription.amount || 0;
  const daysRemaining = getDaysRemainingInGracePeriod(
    subscription.grace_period_ends,
  );

  // Memoize gracePeriodEnd to prevent useEffect dependency issues
  const gracePeriodEnd = useMemo(() => {
    return subscription.grace_period_ends
      ? new Date(subscription.grace_period_ends)
      : null;
  }, [subscription.grace_period_ends]);

  // Determine urgency level
  const urgencyLevel =
    daysRemaining === null || daysRemaining <= 0
      ? 'critical'
      : daysRemaining <= 1
        ? 'high'
        : daysRemaining <= 3
          ? 'medium'
          : 'low';

  // Update countdown timer
  useEffect(() => {
    if (!gracePeriodEnd || !autoRefresh) return;

    const updateTimer = () => {
      const now = new Date();
      const diff = gracePeriodEnd.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining({ days: 0, hours: 0, minutes: 0 });
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      setTimeRemaining({ days, hours, minutes });
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [gracePeriodEnd, autoRefresh]);

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  if (isDismissed) {
    return null;
  }

  const getUrgencyConfig = () => {
    switch (urgencyLevel) {
      case 'critical':
        return {
          bgColor: 'bg-red-50 dark:bg-red-950',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          accentColor: 'text-red-600',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          icon: AlertTriangle,
          title: 'URGENT: Grace Period Expired',
          badge: 'Critical',
          badgeVariant: 'destructive' as const,
        };
      case 'high':
        return {
          bgColor: 'bg-red-50 dark:bg-red-950',
          borderColor: 'border-red-200 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-200',
          accentColor: 'text-red-600',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          icon: Zap,
          title: 'URGENT: Payment Due Today',
          badge: 'Urgent',
          badgeVariant: 'destructive' as const,
        };
      case 'medium':
        return {
          bgColor: 'bg-orange-50 dark:bg-orange-950',
          borderColor: 'border-orange-200 dark:border-orange-800',
          textColor: 'text-orange-800 dark:text-orange-200',
          accentColor: 'text-orange-600',
          buttonColor: 'bg-orange-600 hover:bg-orange-700',
          icon: Clock,
          title: 'Payment Required Soon',
          badge: 'Warning',
          badgeVariant: 'secondary' as const,
        };
      default:
        return {
          bgColor: 'bg-yellow-50 dark:bg-yellow-950',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          textColor: 'text-yellow-800 dark:text-yellow-200',
          accentColor: 'text-yellow-600',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700',
          icon: Clock,
          title: 'Grace Period Active',
          badge: 'Grace Period',
          badgeVariant: 'secondary' as const,
        };
    }
  };

  const config = getUrgencyConfig();
  const IconComponent = config.icon;

  // Calculate progress percentage (7 days total grace period)
  const progressPercentage =
    daysRemaining !== null
      ? Math.max(0, Math.min(100, ((7 - daysRemaining) / 7) * 100))
      : 100;

  // Alert variant
  if (variant === 'alert') {
    return (
      <Alert className={cn(config.bgColor, config.borderColor, className)}>
        <IconComponent className={cn('h-4 w-4', config.accentColor)} />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <p className={cn('font-medium', config.textColor)}>
                {config.title}
              </p>
              <div className="flex items-center gap-4 mt-1">
                <p className={cn('text-sm', config.textColor)}>
                  {timeRemaining
                    ? `${timeRemaining.days}d ${timeRemaining.hours}h ${timeRemaining.minutes}m remaining`
                    : 'Grace period ended'}
                </p>
                <Badge variant={config.badgeVariant} className="text-xs">
                  RM {monthlyAmount.toFixed(2)}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {onPayNow && (
                <Button
                  size="sm"
                  className={config.buttonColor}
                  onClick={onPayNow}
                >
                  Pay Now
                </Button>
              )}
              {showDismiss && (
                <Button size="sm" variant="ghost" onClick={handleDismiss}>
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Card variant
  if (variant === 'card') {
    return (
      <Card
        className={cn(
          config.bgColor,
          config.borderColor,
          'border-2',
          className,
        )}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={cn('p-2 rounded-lg', config.bgColor)}>
                <IconComponent className={cn('h-6 w-6', config.accentColor)} />
              </div>
              <div>
                <CardTitle className={cn('text-lg', config.textColor)}>
                  {config.title}
                </CardTitle>
                <p className={cn('text-sm', config.textColor, 'opacity-80')}>
                  {projectName}
                </p>
              </div>
            </div>
            <Badge variant={config.badgeVariant}>{config.badge}</Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Countdown Display */}
          <div className="text-center p-4 rounded-lg bg-white/50 dark:bg-black/20">
            <div className="flex justify-center items-center gap-4 text-2xl font-mono font-bold">
              {timeRemaining ? (
                <>
                  <div className="text-center">
                    <div className={config.textColor}>{timeRemaining.days}</div>
                    <div className="text-xs opacity-70">days</div>
                  </div>
                  <div className={config.accentColor}>:</div>
                  <div className="text-center">
                    <div className={config.textColor}>
                      {timeRemaining.hours}
                    </div>
                    <div className="text-xs opacity-70">hours</div>
                  </div>
                  <div className={config.accentColor}>:</div>
                  <div className="text-center">
                    <div className={config.textColor}>
                      {timeRemaining.minutes}
                    </div>
                    <div className="text-xs opacity-70">mins</div>
                  </div>
                </>
              ) : (
                <div className={cn('text-xl', config.textColor)}>
                  Grace Period Ended
                </div>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className={config.textColor}>Grace Period Progress</span>
              <span className={config.accentColor}>
                {urgencyLevel === 'critical'
                  ? 'Expired'
                  : `${Math.round(progressPercentage)}%`}
              </span>
            </div>
            <Progress
              value={progressPercentage}
              className={cn(
                'h-2',
                urgencyLevel === 'critical' && '[&>div]:bg-red-500',
                urgencyLevel === 'high' && '[&>div]:bg-red-500',
                urgencyLevel === 'medium' && '[&>div]:bg-orange-500',
                urgencyLevel === 'low' && '[&>div]:bg-yellow-500',
              )}
            />
          </div>

          {/* Payment Info */}
          <div className="flex items-center justify-between p-3 rounded-lg bg-white/30 dark:bg-black/20">
            <div className="flex items-center gap-2">
              <CreditCard className={cn('h-4 w-4', config.accentColor)} />
              <span className={cn('font-medium', config.textColor)}>
                Monthly Payment
              </span>
            </div>
            <span className={cn('text-lg font-bold', config.textColor)}>
              RM {monthlyAmount.toFixed(2)}
            </span>
          </div>

          {/* Action Button */}
          {onPayNow && (
            <Button
              onClick={onPayNow}
              className={cn('w-full', config.buttonColor)}
              size="lg"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {urgencyLevel === 'critical'
                ? 'Restore Access Now'
                : 'Pay Now to Avoid Suspension'}
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}

          {showDismiss && (
            <Button
              variant="ghost"
              onClick={handleDismiss}
              className={cn('w-full', config.textColor)}
            >
              Dismiss Warning
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  // Banner variant (default)
  return (
    <div
      className={cn(
        'w-full border-b-2 animate-pulse',
        config.bgColor,
        config.borderColor,
        urgencyLevel === 'critical' && 'animate-pulse',
        className,
      )}
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={cn('p-2 rounded-lg bg-white/20 dark:bg-black/20')}>
              <IconComponent className={cn('h-5 w-5', config.accentColor)} />
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-3">
                <h3 className={cn('font-bold', config.textColor)}>
                  {config.title} - {projectName}
                </h3>
                <Badge variant={config.badgeVariant} className="text-xs">
                  {config.badge}
                </Badge>
              </div>

              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className={cn('h-4 w-4', config.accentColor)} />
                  <span className={config.textColor}>
                    {timeRemaining
                      ? `${timeRemaining.days}d ${timeRemaining.hours}h ${timeRemaining.minutes}m remaining`
                      : 'Grace period ended'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CreditCard className={cn('h-4 w-4', config.accentColor)} />
                  <span className={config.textColor}>
                    RM {monthlyAmount.toFixed(2)} payment required
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {onPayNow && (
              <Button
                onClick={onPayNow}
                className={config.buttonColor}
                size="sm"
              >
                <Zap className="h-4 w-4 mr-2" />
                {urgencyLevel === 'critical' ? 'Restore Access' : 'Pay Now'}
              </Button>
            )}

            {showDismiss && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDismiss}
                className={cn(
                  'hover:bg-white/20 dark:hover:bg-black/20',
                  config.textColor,
                )}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Mini progress bar for banner */}
        <div className="mt-2">
          <Progress
            value={progressPercentage}
            className={cn(
              'h-1 bg-white/20 dark:bg-black/20',
              urgencyLevel === 'critical' && '[&>div]:bg-red-400',
              urgencyLevel === 'high' && '[&>div]:bg-red-400',
              urgencyLevel === 'medium' && '[&>div]:bg-orange-400',
              urgencyLevel === 'low' && '[&>div]:bg-yellow-400',
            )}
          />
        </div>
      </div>
    </div>
  );
}
