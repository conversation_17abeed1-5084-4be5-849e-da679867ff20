'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useRemoveMember } from '@/features/projects/hooks/use-remove-member';
import { <PERSON><PERSON><PERSON>riangle, Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface RemoveMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  memberId: string;
  memberName: string;
  memberRole: string;
  onSuccess?: () => void;
}

export function RemoveMemberDialog({
  open,
  onOpenChange,
  projectId,
  memberId,
  memberName,
  memberRole,
  onSuccess,
}: RemoveMemberDialogProps) {
  const t = useTranslations('components.removeMemberDialog');
  const removeMemberMutation = useRemoveMember();

  const handleRemove = async () => {
    try {
      await removeMemberMutation.mutateAsync({
        projectId,
        memberId,
      });

      // Show success toast
      toast.success(
        t('successMessage', {
          defaultValue: `${memberName} has been removed from the project`,
          memberName,
        }),
      );

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error('Failed to remove member:', error);

      // Show error toast
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(
        t('errorMessage', {
          defaultValue: `Failed to remove member: ${errorMessage}`,
          error: errorMessage,
        }),
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <DialogTitle>
              {t('title', { defaultValue: 'Remove Team Member' })}
            </DialogTitle>
          </div>
          <DialogDescription className="text-left space-y-2">
            <p>
              {t('description', {
                defaultValue:
                  'Are you sure you want to remove this member from the project?',
              })}
            </p>
            <div className="p-3 bg-muted rounded-md">
              <p className="font-medium text-foreground">{memberName}</p>
              <p className="text-sm text-muted-foreground capitalize">
                {memberRole}
              </p>
            </div>
            <p className="text-sm text-muted-foreground">
              {t('warning', {
                defaultValue:
                  'This member will lose access to the project but can be restored later by an admin.',
              })}
            </p>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={removeMemberMutation.isPending}
          >
            {t('cancel', { defaultValue: 'Cancel' })}
          </Button>
          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={removeMemberMutation.isPending}
          >
            {removeMemberMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {t('confirm', { defaultValue: 'Remove Member' })}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
