/**
 * BillPlz API Types and Interfaces
 * Comprehensive type definitions for BillPlz payment gateway integration
 */

// ================================
// CORE BILLPLZ API TYPES
// ================================

// BillPlz Bill Request (Create Bill)
export interface BillPlzCreateBillRequest {
  collection_id: string;
  email: string;
  mobile?: string;
  name: string;
  amount: number; // Amount in cents (RM 150.00 = 15000 cents)
  callback_url: string;
  description: string;
  due_at?: string; // ISO date string
  redirect_url?: string;
  reference_1_label?: string;
  reference_1?: string;
  reference_2_label?: string;
  reference_2?: string;
}

// BillPlz Bill Response (from API)
export interface BillPlzBill {
  id: string;
  collection_id: string;
  paid: boolean;
  state: 'overdue' | 'open' | 'paid';
  amount: number;
  paid_amount: number;
  due_at: string;
  email: string;
  mobile: string | null;
  name: string;
  url: string;
  reference_1_label: string | null;
  reference_1: string | null;
  reference_2_label: string | null;
  reference_2: string | null;
  redirect_url: string | null;
  callback_url: string;
  description: string;
  paid_at: string | null;
}

// BillPlz Collection Request
export interface BillPlzCreateCollectionRequest {
  title: string;
  logo?: string; // URL to logo image
  split_header?: boolean;
  split_payments?: Array<{
    split_bank_account_id: string;
    fixed_cut?: number;
    variable_cut?: number;
  }>;
}

// BillPlz Collection Response
export interface BillPlzCollection {
  id: string;
  title: string;
  logo?: {
    thumb_url?: string;
    avatar_url?: string;
  };
  split_header: boolean;
  split_payments: Array<{
    split_bank_account_id: string;
    fixed_cut: number;
    variable_cut: number;
  }>;
}

// BillPlz Transaction Response
export interface BillPlzTransaction {
  id: string;
  status: string;
  completed_at: string | null;
  payment_channel: string | null;
  paid_amount: number;
  transaction_id: string | null;
  transaction_status: string | null;
}

// ================================
// ERROR HANDLING TYPES
// ================================

// BillPlz Error Response
export interface BillPlzError {
  error: {
    type: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

// Enhanced Error Response with HTTP context
export interface BillPlzApiError extends Error {
  name: 'BillPlzApiError';
  message: string;
  status: number;
  statusText: string;
  billplzError?: BillPlzError;
  response?: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };
  request?: {
    url: string;
    method: string;
    body?: unknown;
  };
}

// Network Error
export interface BillPlzNetworkError extends Error {
  name: 'BillPlzNetworkError';
  message: string;
  cause?: unknown;
}

// Validation Error
export interface BillPlzValidationError extends Error {
  name: 'BillPlzValidationError';
  message: string;
  field?: string;
  value?: unknown;
}

// Rate Limit Error
export interface BillPlzRateLimitError extends Error {
  name: 'BillPlzRateLimitError';
  message: string;
  retryAfter?: number; // seconds
  limit?: number;
  remaining?: number;
  resetTime?: Date;
}

// ================================
// API RESPONSE WRAPPER TYPES
// ================================

export interface BillPlzApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  billplzError?: BillPlzError;
  meta?: {
    requestId: string;
    timestamp: string;
    rateLimitRemaining?: number;
    rateLimitReset?: string;
  };
}

export interface BillPlzListResponse<T> {
  bills?: T[];
  collections?: T[];
  transactions?: T[];
  page: number;
  size: number;
  total: number;
}

// ================================
// WEBHOOK TYPES
// ================================

// BillPlz Webhook Payload
export interface BillPlzWebhookPayload {
  id: string;
  collection_id: string;
  paid: boolean;
  state: 'overdue' | 'open' | 'paid';
  amount: number;
  paid_amount: number;
  due_at: string;
  email: string;
  mobile: string | null;
  name: string;
  url: string;
  paid_at: string | null;
  x_signature: string;
}

// Webhook verification result
export interface WebhookVerificationResult {
  isValid: boolean;
  error?: string;
  payload?: BillPlzWebhookPayload;
}

// ================================
// RATE LIMITING TYPES
// ================================

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number; // seconds to wait before retry
}

export interface RateLimitState {
  requests: Array<{ timestamp: number; success: boolean }>;
  circuitBreakerFailures: number;
  isCircuitOpen: boolean;
  lastFailureTime: number;
}

// ================================
// CLIENT CONFIGURATION TYPES
// ================================

export interface BillPlzConfig {
  apiKey: string;
  collectionId: string;
  xSignatureKey: string;
  sandboxMode: boolean;
  webhookUrl?: string;
  apiVersion: string;
  baseUrl: string;
  timeout: number;
}

export interface BillPlzClientOptions {
  apiKey: string;
  baseUrl: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  enableLogging?: boolean;
  enableRateLimit?: boolean;
}

// HTTP Method types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request options
export interface RequestOptions {
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: unknown;
  timeout?: number;
  retries?: number;
}

// ================================
// UTILITY TYPES
// ================================

// Bill reference generation options
export interface BillReferenceOptions {
  prefix?: string;
  projectId?: string;
  subscriptionId?: string;
  contractorId?: string;
  timestamp?: boolean;
}

// Amount conversion options
export interface AmountConversionOptions {
  currency?: 'MYR';
  precision?: number;
  validate?: boolean;
  minAmount?: number;
  maxAmount?: number;
}

// Status mapping configuration
export interface StatusMapping {
  billplzState: 'overdue' | 'open' | 'paid';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'cancelled' | 'refunded';
  subscriptionStatus?:
    | 'active'
    | 'pending_payment'
    | 'grace_period'
    | 'cancelled'
    | 'suspended';
}

// ================================
// LOGGING TYPES
// ================================

export interface LogContext {
  requestId: string;
  method: string;
  url: string;
  timestamp: string;
  userAgent?: string;
  clientIp?: string;
}

export interface RequestLog extends LogContext {
  type: 'request';
  headers: Record<string, string>;
  body?: unknown;
  maskedApiKey?: string;
}

export interface ResponseLog extends LogContext {
  type: 'response';
  status: number;
  statusText: string;
  responseTime: number;
  responseSize?: number;
  headers: Record<string, string>;
  body?: unknown;
}

export interface ErrorLog extends LogContext {
  type: 'error';
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  responseTime?: number;
}

// ================================
// TYPE GUARDS
// ================================

export function isBillPlzError(error: unknown): error is BillPlzApiError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'name' in error &&
    (error as { name: string }).name === 'BillPlzApiError'
  );
}

export function isBillPlzNetworkError(
  error: unknown,
): error is BillPlzNetworkError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'name' in error &&
    (error as { name: string }).name === 'BillPlzNetworkError'
  );
}

export function isBillPlzValidationError(
  error: unknown,
): error is BillPlzValidationError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'name' in error &&
    (error as { name: string }).name === 'BillPlzValidationError'
  );
}

export function isBillPlzRateLimitError(
  error: unknown,
): error is BillPlzRateLimitError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'name' in error &&
    (error as { name: string }).name === 'BillPlzRateLimitError'
  );
}

export function isWebhookPayload(
  payload: unknown,
): payload is BillPlzWebhookPayload {
  return (
    typeof payload === 'object' &&
    payload !== null &&
    'id' in payload &&
    'collection_id' in payload &&
    'paid' in payload &&
    'state' in payload &&
    'amount' in payload &&
    'x_signature' in payload &&
    typeof (payload as { id: unknown }).id === 'string' &&
    typeof (payload as { collection_id: unknown }).collection_id === 'string' &&
    typeof (payload as { paid: unknown }).paid === 'boolean' &&
    ['overdue', 'open', 'paid'].includes(
      (payload as { state: unknown }).state as string,
    ) &&
    typeof (payload as { amount: unknown }).amount === 'number' &&
    typeof (payload as { x_signature: unknown }).x_signature === 'string'
  );
}

// ================================
// CONSTANTS
// ================================

export const BILLPLZ_STATES = {
  OPEN: 'open',
  PAID: 'paid',
  OVERDUE: 'overdue',
} as const;

export const BILLPLZ_PAYMENT_CHANNELS = {
  ONLINE_BANKING: 'Online Banking',
  CREDIT_CARD: 'Credit Card',
  E_WALLET: 'e-Wallet',
  RETAIL_OUTLET: 'Retail Outlet',
  BOOST: 'Boost',
  GRABPAY: 'GrabPay',
  SHOPEE_PAY: 'ShopeePay',
  TOUCH_N_GO: "Touch 'n Go eWallet",
} as const;

export const HTTP_STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// ================================
// EXPORTS
// ================================

// Export commonly used types for convenience
export type BillPlzState = (typeof BILLPLZ_STATES)[keyof typeof BILLPLZ_STATES];
export type PaymentChannel =
  (typeof BILLPLZ_PAYMENT_CHANNELS)[keyof typeof BILLPLZ_PAYMENT_CHANNELS];
export type HttpStatusCode =
  (typeof HTTP_STATUS_CODES)[keyof typeof HTTP_STATUS_CODES];
