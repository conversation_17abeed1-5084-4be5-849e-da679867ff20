import { supabase } from '@/lib/supabase';
import { hasPermission } from '@/lib/rbac';
import type { UserRole } from '@/types/auth';
import type { SubscriptionWithAccess } from '@/types/billing';
import {
  checkProjectAccess,
  getDaysRemainingInGracePeriod,
} from '@/types/billing';
import { subscriptionsService } from './subscriptions.service';

export interface ProjectAccessResult {
  hasAccess: boolean;
  reason:
    | 'active'
    | 'grace_period'
    | 'admin_bypass'
    | 'no_subscription'
    | 'suspended'
    | 'expired';
  subscription?: SubscriptionWithAccess | null;
  gracePeriodDays?: number;
  message: string;
}

export interface UserProjectAccess {
  userId: string;
  userRole: UserRole;
  projectId: string;
  hasAccess: boolean;
  accessType: 'role_based' | 'subscription_based' | 'denied';
  subscription?: SubscriptionWithAccess | null;
}

export interface AccessValidationParams {
  userId: string;
  userRole: UserRole;
  projectId: string;
  requireActiveSubscription?: boolean;
}

export interface BulkAccessCheckParams {
  userId: string;
  userRole: UserRole;
  projectIds: string[];
}

export class AccessControlService {
  async checkProjectAccess(
    userId: string,
    projectId: string,
    userRole?: UserRole,
  ): Promise<ProjectAccessResult> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'User not found',
          };
        }
        role = user.user_role as UserRole;
      }

      // Admin users bypass billing restrictions
      if (role === 'admin') {
        return {
          hasAccess: true,
          reason: 'admin_bypass',
          message: 'Admin access granted',
        };
      }

      // For contractors and viewers, check subscription status
      if (role === 'contractor' || role === 'viewer') {
        // Get subscription for this project
        const subscriptionResult =
          await subscriptionsService.getByProjectId(projectId);

        if (subscriptionResult.error) {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: `Error checking subscription: ${subscriptionResult.error}`,
          };
        }

        if (!subscriptionResult.data) {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'No subscription found for this project',
          };
        }

        const subscription = subscriptionResult.data;

        // Verify the user is associated with this subscription
        if (subscription.contractor_id !== userId && role === 'contractor') {
          return {
            hasAccess: false,
            reason: 'no_subscription',
            message: 'User is not the contractor for this subscription',
          };
        }

        // Check subscription access
        const accessAllowed = checkProjectAccess(subscription);
        const isInGracePeriod = subscription.status === 'grace_period';
        const daysUntilSuspension = isInGracePeriod
          ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
          : undefined;

        const subscriptionWithAccess: SubscriptionWithAccess = {
          ...subscription,
          accessAllowed,
          isInGracePeriod,
          daysUntilSuspension: daysUntilSuspension ?? undefined,
        };

        if (accessAllowed) {
          return {
            hasAccess: true,
            reason:
              subscription.status === 'active' ? 'active' : 'grace_period',
            subscription: subscriptionWithAccess,
            gracePeriodDays: daysUntilSuspension ?? undefined,
            message:
              subscription.status === 'active'
                ? 'Active subscription'
                : `Grace period: ${daysUntilSuspension} days remaining`,
          };
        }

        // Determine specific reason for denial
        const reason =
          subscription.status === 'suspended'
            ? ('suspended' as const)
            : subscription.status === 'cancelled'
              ? ('expired' as const)
              : subscription.status === 'grace_period'
                ? ('expired' as const) // Grace period has expired
                : ('no_subscription' as const);

        return {
          hasAccess: false,
          reason,
          subscription: subscriptionWithAccess,
          message: this.getAccessDeniedMessage(subscription.status, null),
        };
      }

      return {
        hasAccess: false,
        reason: 'no_subscription',
        message: 'Invalid user role',
      };
    } catch (error) {
      console.error('Check project access error:', error);
      return {
        hasAccess: false,
        reason: 'no_subscription',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getProjectAccessState(projectId: string): Promise<{
    data: {
      subscription: SubscriptionWithAccess | null;
      accessAllowed: boolean;
      statusSummary: string;
    } | null;
    error: string | null;
  }> {
    try {
      const subscriptionResult =
        await subscriptionsService.getByProjectId(projectId);

      if (subscriptionResult.error) {
        return { data: null, error: subscriptionResult.error };
      }

      if (!subscriptionResult.data) {
        return {
          data: {
            subscription: null,
            accessAllowed: false,
            statusSummary: 'No subscription found',
          },
          error: null,
        };
      }

      const subscription = subscriptionResult.data;
      const accessAllowed = checkProjectAccess(subscription);
      const isInGracePeriod = subscription.status === 'grace_period';
      const daysUntilSuspension = isInGracePeriod
        ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
        : undefined;

      const subscriptionWithAccess: SubscriptionWithAccess = {
        ...subscription,
        accessAllowed,
        isInGracePeriod,
        daysUntilSuspension: daysUntilSuspension ?? undefined,
      };

      const statusSummary = this.generateStatusSummary(subscriptionWithAccess);

      return {
        data: {
          subscription: subscriptionWithAccess,
          accessAllowed,
          statusSummary,
        },
        error: null,
      };
    } catch (error) {
      console.error('Get project access state error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async validateContractorAccess(params: AccessValidationParams): Promise<{
    isValid: boolean;
    userProjectAccess: UserProjectAccess;
    redirectPath?: string;
  }> {
    const {
      userId,
      userRole,
      projectId,
      requireActiveSubscription = true,
    } = params;

    try {
      // Check basic role permissions
      const hasProjectPermission = hasPermission(userRole, 'projects.view');

      if (!hasProjectPermission) {
        return {
          isValid: false,
          userProjectAccess: {
            userId,
            userRole,
            projectId,
            hasAccess: false,
            accessType: 'denied',
          },
          redirectPath: '/dashboard',
        };
      }

      // Admin users have automatic access
      if (userRole === 'admin') {
        return {
          isValid: true,
          userProjectAccess: {
            userId,
            userRole,
            projectId,
            hasAccess: true,
            accessType: 'role_based',
          },
        };
      }

      // Check subscription-based access for contractors
      const accessResult = await this.checkProjectAccess(
        userId,
        projectId,
        userRole,
      );

      // If access is required and user doesn't have it, redirect to billing
      if (requireActiveSubscription && !accessResult.hasAccess) {
        const redirectPath =
          accessResult.reason === 'no_subscription'
            ? `/billing/subscribe?projectId=${projectId}`
            : `/billing/payment?projectId=${projectId}`;

        return {
          isValid: false,
          userProjectAccess: {
            userId,
            userRole,
            projectId,
            hasAccess: false,
            accessType: 'denied',
            subscription: accessResult.subscription,
          },
          redirectPath,
        };
      }

      return {
        isValid: true,
        userProjectAccess: {
          userId,
          userRole,
          projectId,
          hasAccess: accessResult.hasAccess,
          accessType: 'subscription_based',
          subscription: accessResult.subscription,
        },
      };
    } catch (error) {
      console.error('Validate contractor access error:', error);
      return {
        isValid: false,
        userProjectAccess: {
          userId,
          userRole,
          projectId,
          hasAccess: false,
          accessType: 'denied',
        },
        redirectPath: '/dashboard',
      };
    }
  }

  async bulkCheckProjectAccess(params: BulkAccessCheckParams): Promise<{
    accessResults: Map<string, ProjectAccessResult>;
    error: string | null;
  }> {
    const { userId, userRole, projectIds } = params;
    const accessResults = new Map<string, ProjectAccessResult>();

    try {
      // Admin users have access to all projects
      if (userRole === 'admin') {
        for (const projectId of projectIds) {
          accessResults.set(projectId, {
            hasAccess: true,
            reason: 'admin_bypass',
            message: 'Admin access granted',
          });
        }
        return { accessResults, error: null };
      }

      // Check access for each project
      const accessPromises = projectIds.map(async (projectId) => {
        const result = await this.checkProjectAccess(
          userId,
          projectId,
          userRole,
        );
        return { projectId, result };
      });

      const results = await Promise.all(accessPromises);

      for (const { projectId, result } of results) {
        accessResults.set(projectId, result);
      }

      return { accessResults, error: null };
    } catch (error) {
      console.error('Bulk check project access error:', error);
      return {
        accessResults,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async revokeProjectAccess(
    subscriptionId: string,
    reason: string = 'Access revoked',
  ): Promise<{ success: boolean; error: string | null }> {
    try {
      const result = await subscriptionsService.suspendSubscription(
        subscriptionId,
        reason,
      );

      if (result.error) {
        return { success: false, error: result.error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Revoke project access error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async restoreProjectAccess(subscriptionId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const result =
        await subscriptionsService.reactivateSubscription(subscriptionId);

      if (result.error) {
        return { success: false, error: result.error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Restore project access error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getUserAccessibleProjects(
    userId: string,
    userRole?: UserRole,
  ): Promise<{ projectIds: string[]; error: string | null }> {
    try {
      // Get user role if not provided
      let role = userRole;
      if (!role) {
        const { data: user } = await supabase
          .from('users')
          .select('user_role')
          .eq('id', userId)
          .single();

        if (!user) {
          return { projectIds: [], error: 'User not found' };
        }
        role = user.user_role as UserRole;
      }

      // Admin users can access all projects
      if (role === 'admin') {
        const { data: projects } = await supabase.from('projects').select('id');

        return {
          projectIds: projects?.map((p) => p.id) || [],
          error: null,
        };
      }

      // For contractors, get projects with valid subscriptions
      if (role === 'contractor') {
        const subscriptionsResult =
          await subscriptionsService.getAllWithFilters({
            contractorId: userId,
            accessAllowed: true,
          });

        if (subscriptionsResult.error) {
          return { projectIds: [], error: subscriptionsResult.error };
        }

        return {
          projectIds: subscriptionsResult.data.map((sub) => sub.project_id),
          error: null,
        };
      }

      return { projectIds: [], error: null };
    } catch (error) {
      console.error('Get user accessible projects error:', error);
      return {
        projectIds: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private getAccessDeniedMessage(
    status: string,
    failureReason: string | null = null,
  ): string {
    switch (status) {
      case 'suspended':
        return failureReason || 'Project access has been suspended';
      case 'cancelled':
        return 'Subscription has been cancelled';
      case 'pending_payment':
        return 'Payment is pending for this project';
      case 'grace_period':
        return 'Grace period has expired';
      default:
        return 'Access denied due to subscription status';
    }
  }

  private generateStatusSummary(subscription: SubscriptionWithAccess): string {
    const { status, accessAllowed, isInGracePeriod, daysUntilSuspension } =
      subscription;

    if (status === 'active') {
      return 'Active subscription - full access';
    }

    if (isInGracePeriod && accessAllowed) {
      return `Grace period - ${daysUntilSuspension} days remaining`;
    }

    if (status === 'pending_payment') {
      return 'Payment pending - access limited';
    }

    if (status === 'suspended') {
      return 'Subscription suspended - no access';
    }

    if (status === 'cancelled') {
      return 'Subscription cancelled - no access';
    }

    return `Subscription ${status} - ${accessAllowed ? 'access allowed' : 'no access'}`;
  }
}

export const accessControlService = new AccessControlService();
