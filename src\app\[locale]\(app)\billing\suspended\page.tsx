'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { useSubscriptionsRequiringAttention } from '@/features/billing/hooks';
import type { SubscriptionWithAccess } from '@/types/billing';
import {
  convertCentsToMyr,
  getDaysRemainingInGracePeriod,
} from '@/types/billing';
import { differenceInDays, format } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  DollarSign,
  Mail,
  Users,
  Zap,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

/**
 * Dedicated page for suspended account management
 * List of suspended projects with payment options
 * Bulk payment options for multiple projects
 * Account restoration workflow
 */
export default function SuspendedProjectsPage() {
  const t = useTranslations('billing.suspended');
  const router = useRouter();

  const [selectedProjects, setSelectedProjects] = useState<Set<string>>(
    new Set(),
  );

  // Data fetching
  const {
    data: suspendedSubscriptions,
    isLoading: subscriptionsLoading,
    error: subscriptionsError,
    refetch: refetchSubscriptions,
  } = useSubscriptionsRequiringAttention();

  const isLoading = subscriptionsLoading;

  // Filter to only suspended/grace period subscriptions
  const filteredSubscriptions = useMemo(() => {
    if (!suspendedSubscriptions) return [];
    return suspendedSubscriptions.filter(
      (sub) =>
        sub.status === 'suspended' ||
        sub.status === 'grace_period' ||
        !sub.accessAllowed,
    );
  }, [suspendedSubscriptions]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalSuspended = filteredSubscriptions.length;
    const totalOwed = filteredSubscriptions.reduce(
      (sum, sub) => sum + (sub.amount || 0),
      0,
    );
    const oldestSuspension = filteredSubscriptions.reduce((oldest, sub) => {
      const suspendedDate = sub.grace_period_ends
        ? new Date(sub.grace_period_ends)
        : new Date(sub.updated_at || sub.created_at || 0);
      return suspendedDate < oldest ? suspendedDate : oldest;
    }, new Date());

    return {
      totalSuspended,
      totalOwed: convertCentsToMyr(totalOwed),
      oldestSuspension: totalSuspended > 0 ? oldestSuspension : null,
    };
  }, [filteredSubscriptions]);

  // Selected projects total
  const selectedTotal = useMemo(() => {
    const selectedSubs = filteredSubscriptions.filter((sub) =>
      selectedProjects.has(sub.id),
    );
    return convertCentsToMyr(
      selectedSubs.reduce((sum, sub) => sum + (sub.amount || 0), 0),
    );
  }, [filteredSubscriptions, selectedProjects]);

  // Handlers
  const handleBack = () => {
    router.push('/billing');
  };

  const handleSelectAll = () => {
    if (selectedProjects.size === filteredSubscriptions.length) {
      setSelectedProjects(new Set());
    } else {
      setSelectedProjects(new Set(filteredSubscriptions.map((sub) => sub.id)));
    }
  };

  const handleSelectProject = (subscriptionId: string) => {
    const newSelected = new Set(selectedProjects);
    if (newSelected.has(subscriptionId)) {
      newSelected.delete(subscriptionId);
    } else {
      newSelected.add(subscriptionId);
    }
    setSelectedProjects(newSelected);
  };

  const handlePayNow = (subscriptionId: string) => {
    const subscription = filteredSubscriptions.find(
      (sub) => sub.id === subscriptionId,
    );
    if (subscription?.project_id) {
      router.push(`/billing/setup/${subscription.project_id}`);
    }
  };

  const handlePaySelected = () => {
    if (selectedProjects.size === 1) {
      const subscriptionId = Array.from(selectedProjects)[0];
      handlePayNow(subscriptionId);
    } else {
      // Handle bulk payment - would need a bulk payment flow
      alert('Bulk payment feature coming soon');
    }
  };

  const handlePayAll = () => {
    // Handle pay all outstanding
    alert('Pay all feature coming soon');
  };

  const handleContactSupport = () => {
    window.open('mailto:<EMAIL>', '_blank');
  };

  const handleViewDetails = (subscriptionId: string) => {
    router.push(`/billing/subscription/${subscriptionId}`);
  };

  const getDaysSuspended = (subscription: SubscriptionWithAccess) => {
    const suspendedDate = subscription.grace_period_ends
      ? new Date(subscription.grace_period_ends)
      : new Date(subscription.updated_at || subscription.created_at || 0);
    return differenceInDays(new Date(), suspendedDate);
  };

  const getUrgencyLevel = (subscription: SubscriptionWithAccess) => {
    if (subscription.status === 'suspended') return 'critical';
    if (subscription.isInGracePeriod) {
      const daysLeft = subscription.grace_period_ends
        ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
        : 0;
      return daysLeft && daysLeft <= 1 ? 'high' : 'medium';
    }
    return 'medium';
  };

  // Loading state
  if (isLoading) {
    return <SuspendedPageSkeleton />;
  }

  // Error state
  if (subscriptionsError) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load suspended projects. Please try again.
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetchSubscriptions()}
              className="ml-2"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Empty state
  if (filteredSubscriptions.length === 0) {
    return (
      <div className="max-w-4xl mx-auto py-8">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={handleBack} size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>
        </div>

        <Card>
          <CardHeader className="text-center">
            <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
            <CardTitle>{t('empty.title')}</CardTitle>
            <CardDescription>{t('empty.description')}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={handleBack}>{t('empty.backToDashboard')}</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={handleBack} size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">{t('subtitle')}</p>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('overview.totalSuspended')}
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {totals.totalSuspended}
            </div>
            <p className="text-xs text-muted-foreground">
              projects requiring attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('overview.totalOwed')}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              RM {totals.totalOwed.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              total outstanding amount
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('overview.oldestSuspension')}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totals.oldestSuspension
                ? `${differenceInDays(new Date(), totals.oldestSuspension)}d`
                : '0d'}
            </div>
            <p className="text-xs text-muted-foreground">oldest suspension</p>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('bulkActions.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={
                    selectedProjects.size === filteredSubscriptions.length
                  }
                  onCheckedChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="text-sm font-medium">
                  {t('bulkActions.selectAll')}
                </label>
              </div>
              <Badge variant="secondary">
                {t('bulkActions.selected', { count: selectedProjects.size })}
              </Badge>
              {selectedProjects.size > 0 && (
                <span className="text-sm font-medium">
                  {t('bulkActions.totalSelected', {
                    amount: selectedTotal.toFixed(2),
                  })}
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handlePaySelected}
                disabled={selectedProjects.size === 0}
                className="bg-red-600 hover:bg-red-700"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                {selectedProjects.size === 1
                  ? t('actions.payNow')
                  : t('bulkActions.paySelected')}
              </Button>
              <Button onClick={handlePayAll} variant="outline">
                {t('actions.payAll')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Projects List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {t('projectsList.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredSubscriptions.map((subscription) => {
              const urgency = getUrgencyLevel(subscription);
              const daysSuspended = getDaysSuspended(subscription);
              const monthlyAmount = convertCentsToMyr(subscription.amount || 0);

              return (
                <div
                  key={subscription.id}
                  className={`p-4 border rounded-lg ${
                    urgency === 'critical'
                      ? 'border-red-200 bg-red-50 dark:bg-red-950'
                      : urgency === 'high'
                        ? 'border-orange-200 bg-orange-50 dark:bg-orange-950'
                        : 'border-yellow-200 bg-yellow-50 dark:bg-yellow-950'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Checkbox
                        checked={selectedProjects.has(subscription.id)}
                        onCheckedChange={() =>
                          handleSelectProject(subscription.id)
                        }
                      />
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">
                            Project #
                            {subscription.project_id?.slice(0, 8) || 'Unknown'}
                          </h3>
                          <Badge
                            variant={
                              urgency === 'critical'
                                ? 'destructive'
                                : urgency === 'high'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                          >
                            {subscription.status}
                          </Badge>
                          {subscription.isInGracePeriod && (
                            <Badge
                              variant="outline"
                              className="text-orange-600"
                            >
                              Grace Period
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-6 mt-1 text-sm text-muted-foreground">
                          <span>
                            <Calendar className="h-3 w-3 inline mr-1" />
                            {t('projectsList.suspendedDate')}:{' '}
                            {subscription.grace_period_ends
                              ? format(
                                  new Date(subscription.grace_period_ends),
                                  'MMM dd, yyyy',
                                )
                              : 'Unknown'}
                          </span>
                          <span>
                            <Clock className="h-3 w-3 inline mr-1" />
                            {t('projectsList.daysSuspended')}: {daysSuspended}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="font-semibold">
                          RM {monthlyAmount.toFixed(2)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t('projectsList.amountOwed')}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewDetails(subscription.id)}
                        >
                          {t('actions.viewDetails')}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handlePayNow(subscription.id)}
                          className={
                            urgency === 'critical'
                              ? 'bg-red-600 hover:bg-red-700'
                              : 'bg-orange-600 hover:bg-orange-700'
                          }
                        >
                          <Zap className="h-4 w-4 mr-1" />
                          {t('actions.payNow')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Account Restoration Section */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {t('restoration.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            {t('restoration.description')}
          </p>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">
                {t('restoration.priorityProjects')}
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {filteredSubscriptions
                  .filter((sub) => getUrgencyLevel(sub) === 'critical')
                  .slice(0, 3)
                  .map((sub) => (
                    <li key={sub.id}>
                      • Project #{sub.project_id?.slice(0, 8)} - RM{' '}
                      {convertCentsToMyr(sub.amount || 0).toFixed(2)}
                    </li>
                  ))}
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">{t('restoration.paymentPlan')}</h4>
              <p className="text-sm text-muted-foreground">
                {t('restoration.contactSupport')}
              </p>
              <Button variant="outline" onClick={handleContactSupport}>
                <Mail className="h-4 w-4 mr-2" />
                {t('actions.contactSupport')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Loading skeleton for suspended projects page
 */
function SuspendedPageSkeleton() {
  return (
    <div className="max-w-6xl mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Skeleton className="h-9 w-16" />
        <div>
          <Skeleton className="h-9 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3 mb-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="mb-6">
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-12 w-full" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
