import { accessControlService } from '@/features/billing/services/access-control.service';
import { projectIntegrationService } from '@/features/billing/services/project-integration.service';
import { hasPermission } from '@/lib/rbac';
import { getUser } from '@/lib/supabase-server';
import type { Database } from '@/types/database';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

type User = Database['public']['Tables']['users']['Row'];
type UserRole = Database['public']['Enums']['user_role'];

const validateAccessSchema = z.object({
  userId: z.string().uuid(),
  projectId: z.string().uuid(),
  userRole: z.enum(['admin', 'contractor', 'viewer']).optional(),
  requireActiveSubscription: z.boolean().default(true),
  resource: z.string().optional(),
  action: z.string().optional(),
});

const bulkValidateSchema = z.object({
  validations: z
    .array(
      z.object({
        id: z.string(), // identifier for each validation
        userId: z.string().uuid(),
        projectId: z.string().uuid(),
        userRole: z.enum(['admin', 'contractor', 'viewer']).optional(),
        requireActiveSubscription: z.boolean().default(true),
      }),
    )
    .max(50), // Limit bulk validations to prevent abuse
});

const middlewareValidationSchema = z.object({
  path: z.string(),
  userId: z.string().uuid(),
  userRole: z.enum(['admin', 'contractor', 'viewer']),
  projectIds: z.array(z.string().uuid()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const { data: user, error: userError } = await getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(user.user_role, 'projects.view')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to validate access' },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const validationType = searchParams.get('type') || 'single';

    const body = await request.json();

    switch (validationType) {
      case 'single':
        return await handleSingleValidation(body, user);

      case 'bulk':
        return await handleBulkValidation(body, user);

      case 'middleware':
        return await handleMiddlewareValidation(body, user);

      default:
        return NextResponse.json(
          { error: 'Invalid validation type' },
          { status: 400 },
        );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 },
      );
    }

    console.error('POST /api/billing/access/validate error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

async function handleSingleValidation(body: unknown, user: User) {
  const validatedData = validateAccessSchema.parse(body);

  // For non-admin users, validate they can only check their own access
  if (user.user_role === 'contractor' && validatedData.userId !== user.id) {
    return NextResponse.json(
      { error: 'Cannot validate access for other users' },
      { status: 403 },
    );
  }

  // Validate project exists
  const projectResult = await projectIntegrationService.getProject(
    validatedData.projectId,
  );

  if (projectResult.error || !projectResult.data) {
    return NextResponse.json({ error: 'Project not found' }, { status: 404 });
  }

  const project = projectResult.data;

  // Determine user role if not provided
  const userRole = validatedData.userRole || user.user_role;

  // Validate contractor access
  const accessValidation = await accessControlService.validateContractorAccess({
    userId: validatedData.userId,
    userRole: userRole as UserRole,
    projectId: validatedData.projectId,
    requireActiveSubscription: validatedData.requireActiveSubscription,
  });

  // Get additional context
  const accessCheck = await accessControlService.checkProjectAccess(
    validatedData.userId,
    validatedData.projectId,
    userRole as UserRole,
  );

  return NextResponse.json({
    data: {
      validation: {
        isValid: accessValidation.isValid,
        userProjectAccess: accessValidation.userProjectAccess,
        redirectPath: accessValidation.redirectPath,
      },
      accessDetails: {
        hasAccess: accessCheck.hasAccess,
        reason: accessCheck.reason,
        message: accessCheck.message,
        gracePeriodDays: accessCheck.gracePeriodDays,
        subscription: accessCheck.subscription,
      },
      project: {
        id: project.id,
        name: project.name,
      },
      user: {
        id: validatedData.userId,
        role: userRole,
      },
      validatedAt: new Date().toISOString(),
    },
    message: accessValidation.isValid
      ? 'Access validated successfully'
      : 'Access validation failed',
  });
}

async function handleBulkValidation(body: unknown, user: User) {
  const validatedData = bulkValidateSchema.parse(body);

  // For non-admin users, validate they can only check their own access
  if (user.user_role === 'contractor') {
    const unauthorizedValidations = validatedData.validations.filter(
      (v) => v.userId !== user.id,
    );

    if (unauthorizedValidations.length > 0) {
      return NextResponse.json(
        { error: 'Cannot validate access for other users' },
        { status: 403 },
      );
    }
  }

  const results = await Promise.all(
    validatedData.validations.map(async (validation) => {
      try {
        // Get project info
        const projectResult = await projectIntegrationService.getProject(
          validation.projectId,
        );

        if (projectResult.error || !projectResult.data) {
          return {
            id: validation.id,
            success: false,
            error: 'Project not found',
          };
        }

        const project = projectResult.data;
        const userRole = validation.userRole || user.user_role;

        // Validate access
        const accessValidation =
          await accessControlService.validateContractorAccess({
            userId: validation.userId,
            userRole: userRole as UserRole,
            projectId: validation.projectId,
            requireActiveSubscription: validation.requireActiveSubscription,
          });

        const accessCheck = await accessControlService.checkProjectAccess(
          validation.userId,
          validation.projectId,
          userRole as UserRole,
        );

        return {
          id: validation.id,
          success: true,
          data: {
            validation: {
              isValid: accessValidation.isValid,
              userProjectAccess: accessValidation.userProjectAccess,
              redirectPath: accessValidation.redirectPath,
            },
            accessDetails: {
              hasAccess: accessCheck.hasAccess,
              reason: accessCheck.reason,
              message: accessCheck.message,
            },
            project: {
              id: project.id,
              name: project.name,
            },
          },
        };
      } catch (error) {
        return {
          id: validation.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  const successCount = results.filter((r) => r.success).length;
  const failureCount = results.length - successCount;

  return NextResponse.json({
    data: {
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount,
      },
      validatedAt: new Date().toISOString(),
    },
    message: `Bulk validation completed: ${successCount} successful, ${failureCount} failed`,
  });
}

async function handleMiddlewareValidation(body: unknown, user: User) {
  const validatedData = middlewareValidationSchema.parse(body);

  // For non-admin users, validate they can only check their own access
  if (user.user_role === 'contractor' && validatedData.userId !== user.id) {
    return NextResponse.json(
      { error: 'Cannot validate access for other users' },
      { status: 403 },
    );
  }

  // Get basic role permissions check
  const hasBasicPermission = hasPermission(
    validatedData.userRole,
    'projects.view',
  );

  if (!hasBasicPermission) {
    return NextResponse.json({
      data: {
        middleware: {
          shouldAllow: false,
          reason: 'insufficient_role_permissions',
          redirectPath: '/dashboard',
          message: 'User role does not have sufficient permissions',
        },
        user: {
          id: validatedData.userId,
          role: validatedData.userRole,
        },
        path: validatedData.path,
        validatedAt: new Date().toISOString(),
      },
    });
  }

  // Admin users have automatic access
  if (validatedData.userRole === 'admin') {
    return NextResponse.json({
      data: {
        middleware: {
          shouldAllow: true,
          reason: 'admin_access',
          message: 'Admin users have automatic access',
        },
        user: {
          id: validatedData.userId,
          role: validatedData.userRole,
        },
        path: validatedData.path,
        validatedAt: new Date().toISOString(),
      },
    });
  }

  // For contractors with specific projects, check subscription access
  if (validatedData.projectIds && validatedData.projectIds.length > 0) {
    const bulkAccessResult = await accessControlService.bulkCheckProjectAccess({
      userId: validatedData.userId,
      userRole: validatedData.userRole,
      projectIds: validatedData.projectIds,
    });

    if (bulkAccessResult.error) {
      return NextResponse.json({
        data: {
          middleware: {
            shouldAllow: false,
            reason: 'access_check_error',
            message: bulkAccessResult.error,
            redirectPath: '/dashboard',
          },
          user: {
            id: validatedData.userId,
            role: validatedData.userRole,
          },
          path: validatedData.path,
          validatedAt: new Date().toISOString(),
        },
      });
    }

    // Check if user has access to at least one project
    const accessResults = Array.from(bulkAccessResult.accessResults.values());
    const hasAnyAccess = accessResults.some((result) => result.hasAccess);

    if (!hasAnyAccess) {
      // Find first project that needs subscription
      const needsSubscription = accessResults.find(
        (result) => result.reason === 'no_subscription',
      );

      const needsPayment = accessResults.find(
        (result) =>
          result.reason === 'suspended' || result.reason === 'expired',
      );

      const redirectPath = needsSubscription
        ? `/billing/setup/${validatedData.projectIds[0]}`
        : needsPayment
          ? `/billing/payment/${validatedData.projectIds[0]}`
          : '/dashboard';

      return NextResponse.json({
        data: {
          middleware: {
            shouldAllow: false,
            reason: 'no_project_access',
            redirectPath,
            message:
              'User does not have access to any of the required projects',
            projectAccessResults: Object.fromEntries(
              bulkAccessResult.accessResults,
            ),
          },
          user: {
            id: validatedData.userId,
            role: validatedData.userRole,
          },
          path: validatedData.path,
          validatedAt: new Date().toISOString(),
        },
      });
    }

    return NextResponse.json({
      data: {
        middleware: {
          shouldAllow: true,
          reason: 'subscription_access',
          message: 'User has valid subscription access',
          projectAccessResults: Object.fromEntries(
            bulkAccessResult.accessResults,
          ),
        },
        user: {
          id: validatedData.userId,
          role: validatedData.userRole,
        },
        path: validatedData.path,
        validatedAt: new Date().toISOString(),
      },
    });
  }

  // Default allow for valid role-based access
  return NextResponse.json({
    data: {
      middleware: {
        shouldAllow: true,
        reason: 'role_based_access',
        message: 'User has valid role-based access',
      },
      user: {
        id: validatedData.userId,
        role: validatedData.userRole,
      },
      path: validatedData.path,
      validatedAt: new Date().toISOString(),
    },
  });
}
