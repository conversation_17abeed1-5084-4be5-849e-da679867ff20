'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import type { ContractorBillingData } from '@/features/billing/types';
import {
  AlertTriangle,
  Building2,
  DollarSign,
  FolderOpen,
  RefreshCw,
  Shield,
  Zap,
} from 'lucide-react';
import { ProjectPmaCard } from './ProjectPmaCard';

export interface ContractorProjectOverviewProps {
  contractorData: ContractorBillingData;
  isLoading?: boolean;
  onPayNow?: (subscriptionId: string) => void;
  onViewProject?: (projectId: string) => void;
  onManageSubscription?: (subscriptionId: string) => void;
  onCancel?: (subscriptionId: string) => void;
  onReactivate?: (subscriptionId: string) => void;
  onUpdatePaymentMethod?: (subscriptionId: string) => void;
  onViewPaymentHistory?: (subscriptionId: string) => void;
  onUpdateBilling?: (subscriptionId: string) => void;
  onRefresh?: () => void;
  className?: string;
}

export function ContractorProjectOverview({
  contractorData,
  isLoading = false,
  onPayNow,
  onViewProject,
  onManageSubscription,
  onCancel,
  onReactivate,
  onUpdatePaymentMethod,
  onViewPaymentHistory,
  onUpdateBilling,
  onRefresh,
  className,
}: ContractorProjectOverviewProps) {
  if (isLoading) {
    return <ContractorOverviewSkeleton />;
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Contractor Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl bg-primary/10">
            <Building2 className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              {contractorData.contractor_name}
            </h2>
            <p className="text-muted-foreground">
              {contractorData.total_projects} project
              {contractorData.total_projects !== 1 ? 's' : ''} •{' '}
              {contractorData.total_pmas} PMA
              {contractorData.total_pmas !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        {onRefresh && (
          <Button onClick={onRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      {/* Urgent Actions Alert */}
      {contractorData.has_urgent_payments && (
        <Alert
          variant="destructive"
          className="border-red-200 bg-red-50 dark:bg-red-950"
        >
          <Zap className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Urgent Payment Required</p>
                <p className="text-sm">
                  {contractorData.urgent_subscriptions_count} PMA subscription
                  {contractorData.urgent_subscriptions_count !== 1
                    ? 's require'
                    : ' requires'}{' '}
                  immediate attention
                </p>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Contractor Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total PMAs</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {contractorData.total_pmas}
            </div>
            <p className="text-xs text-muted-foreground">
              Across {contractorData.total_projects} project
              {contractorData.total_projects !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active PMAs</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {contractorData.total_active_pmas}
            </div>
            <p className="text-xs text-muted-foreground">
              {(
                (contractorData.total_active_pmas / contractorData.total_pmas) *
                100
              ).toFixed(1)}
              % of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              RM {contractorData.total_monthly_amount.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">Per month billing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issues</CardTitle>
            <AlertTriangle
              className={cn(
                'h-4 w-4',
                contractorData.has_urgent_payments
                  ? 'text-red-600'
                  : 'text-muted-foreground',
              )}
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {contractorData.total_suspended_pmas}
            </div>
            <p className="text-xs text-muted-foreground">Suspended PMAs</p>
            {contractorData.has_urgent_payments && (
              <Badge variant="destructive" className="text-xs mt-1">
                Urgent
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Projects & PMAs</h3>
            <p className="text-sm text-muted-foreground">
              Manage PMA subscriptions by project
            </p>
          </div>
        </div>

        {contractorData.projects.length === 0 ? (
          <Card>
            <CardContent className="py-12 text-center">
              <FolderOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Projects Found</h3>
              <p className="text-muted-foreground">
                No projects with PMA subscriptions found for this contractor.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {contractorData.projects.map((project) => (
              <ProjectPmaCard
                key={project.project_id}
                project={project}
                onPayNow={onPayNow}
                onViewProject={onViewProject}
                onManageSubscription={onManageSubscription}
                onCancel={onCancel}
                onReactivate={onReactivate}
                onUpdatePaymentMethod={onUpdatePaymentMethod}
                onViewPaymentHistory={onViewPaymentHistory}
                onUpdateBilling={onUpdateBilling}
                defaultExpanded={project.has_urgent_payments}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Loading skeleton for contractor overview
 */
function ContractorOverviewSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex items-center gap-3">
        <Skeleton className="h-12 w-12 rounded-xl" />
        <div>
          <Skeleton className="h-7 w-48 mb-2" />
          <Skeleton className="h-5 w-32" />
        </div>
      </div>

      {/* Stats skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Projects skeleton */}
      <div className="space-y-4">
        <div>
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-48" />
        </div>

        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Skeleton className="h-9 w-9 rounded-lg" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-48 mb-2" />
                  <Skeleton className="h-4 w-64" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    </div>
  );
}
