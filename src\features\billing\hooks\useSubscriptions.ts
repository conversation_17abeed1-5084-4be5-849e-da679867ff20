import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import type {
  ProjectSubscription,
  SubscriptionWithAccess,
  SubscriptionStatus,
} from '@/types/billing';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { subscriptionsService } from '../services';
import { useInvalidateProjectAccess } from './useProjectAccess';

// ================================
// SUBSCRIPTION QUERIES
// ================================

/**
 * Fetch contractor's subscriptions with access status
 */
export function useContractorSubscriptions() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['contractor-subscriptions', contractorId],
    queryFn: async (): Promise<SubscriptionWithAccess[]> => {
      if (!contractorId) return [];

      const result = await subscriptionsService.getAllWithFilters({
        contractorId,
      });

      if (result.error) {
        throw new Error(`Failed to fetch subscriptions: ${result.error}`);
      }

      return result.data;
    },
    enabled: !!user && !!contractorId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Fetch a specific subscription by project ID
 */
export function useProjectSubscription(projectId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['project-subscription', projectId],
    queryFn: async (): Promise<SubscriptionWithAccess | null> => {
      if (!projectId) return null;

      const result = await subscriptionsService.getByProjectId(projectId);

      if (result.error) {
        throw new Error(`Failed to fetch subscription: ${result.error}`);
      }

      if (!result.data) return null;

      // Get subscription with access status
      const accessResult = await subscriptionsService.getSubscriptionWithAccess(
        result.data.id,
      );

      if (accessResult.error) {
        throw new Error(`Failed to get access status: ${accessResult.error}`);
      }

      return accessResult.data;
    },
    enabled: !!user && !!projectId,
    staleTime: 30 * 1000, // 30 seconds - more frequent for access checks
    refetchInterval: 60 * 1000, // Refetch every minute for real-time access updates
  });
}

/**
 * Fetch subscription statistics for contractor dashboard
 */
export function useSubscriptionStats() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['subscription-stats', contractorId],
    queryFn: async () => {
      if (!contractorId) return null;

      const result = await subscriptionsService.getAllWithFilters({
        contractorId,
      });

      if (result.error) {
        throw new Error(`Failed to fetch subscription stats: ${result.error}`);
      }

      // Calculate stats from subscriptions
      const subscriptions = result.data;
      const totalSubscriptions = subscriptions.length;
      const activeSubscriptions = subscriptions.filter(
        (sub) => sub.status === 'active',
      ).length;
      const gracePeriodSubscriptions = subscriptions.filter(
        (sub) => sub.isInGracePeriod,
      ).length;
      const suspendedSubscriptions = subscriptions.filter(
        (sub) => sub.status === 'suspended',
      ).length;
      const totalMonthlyAmount = subscriptions
        .filter((sub) => sub.status === 'active')
        .reduce((sum, sub) => sum + sub.amount, 0);

      return {
        totalSubscriptions,
        activeSubscriptions,
        gracePeriodSubscriptions,
        suspendedSubscriptions,
        totalMonthlyAmount,
        subscriptionsWithAccess: subscriptions.filter(
          (sub) => sub.accessAllowed,
        ).length,
      };
    },
    enabled: !!user && !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get subscriptions that require attention (grace period, overdue, etc.)
 */
export function useSubscriptionsRequiringAttention() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['subscriptions-requiring-attention', contractorId],
    queryFn: async (): Promise<SubscriptionWithAccess[]> => {
      if (!contractorId) return [];

      const result = await subscriptionsService.getAllWithFilters({
        contractorId,
      });

      if (result.error) {
        throw new Error(`Failed to fetch subscriptions: ${result.error}`);
      }

      // Filter subscriptions that need attention
      return result.data.filter((subscription) => {
        // Grace period subscriptions
        if (subscription.isInGracePeriod) return true;

        // Suspended or cancelled subscriptions
        if (
          subscription.status === 'suspended' ||
          subscription.status === 'cancelled'
        )
          return true;

        // Overdue payments (next billing date in past)
        if (
          subscription.next_billing_date &&
          subscription.status === 'active'
        ) {
          const nextBilling = new Date(subscription.next_billing_date);
          const now = new Date();
          if (nextBilling < now) return true;
        }

        return false;
      });
    },
    enabled: !!user && !!contractorId,
    staleTime: 1 * 60 * 1000, // 1 minute - frequent updates for urgent items
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

// ================================
// SUBSCRIPTION MUTATIONS
// ================================

/**
 * Create a new subscription for a project
 */
export function useCreateSubscriptionForProject() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      projectId: string;
      status?: SubscriptionStatus;
      nextBillingDate?: Date;
    }): Promise<ProjectSubscription> => {
      if (!user?.profile?.contractor_id) {
        throw new Error('User is not a contractor');
      }

      const result = await subscriptionsService.createSubscriptionForProject({
        projectId: params.projectId,
        contractorId: user.profile.contractor_id,
        status: params.status,
        nextBillingDate: params.nextBillingDate,
      });

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({
        queryKey: ['project-subscription', variables.projectId],
      });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      // Invalidate project access queries
      invalidateProjectAccess(variables.projectId);

      toast({
        title: 'Subscription Created',
        description: 'Project subscription has been created successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Subscription',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Update subscription status
 */
export function useUpdateSubscriptionStatus() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      subscriptionId: string;
      projectId?: string;
      status: SubscriptionStatus;
      gracePeriodEnds?: Date | null;
      failureReason?: string | null;
    }): Promise<ProjectSubscription> => {
      const result = await subscriptionsService.updateSubscriptionAccess({
        subscriptionId: params.subscriptionId,
        status: params.status,
        gracePeriodEnds: params.gracePeriodEnds,
        failureReason: params.failureReason,
      });

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      if (variables.projectId) {
        queryClient.invalidateQueries({
          queryKey: ['project-subscription', variables.projectId],
        });
        invalidateProjectAccess(variables.projectId);
      }

      toast({
        title: 'Subscription Updated',
        description: 'Subscription status has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Update Subscription',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Cancel a subscription
 */
export function useCancelSubscription() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      subscriptionId: string;
      projectId?: string;
    }): Promise<ProjectSubscription> => {
      const result = await subscriptionsService.updateSubscriptionAccess({
        subscriptionId: params.subscriptionId,
        status: 'cancelled',
        gracePeriodEnds: null,
        failureReason: 'Subscription cancelled by user',
      });

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      if (variables.projectId) {
        queryClient.invalidateQueries({
          queryKey: ['project-subscription', variables.projectId],
        });
        invalidateProjectAccess(variables.projectId);
      }

      toast({
        title: 'Subscription Cancelled',
        description: 'The subscription has been cancelled successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Cancel Subscription',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Reactivate a subscription
 */
export function useReactivateSubscription() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      subscriptionId: string;
      projectId?: string;
    }): Promise<ProjectSubscription> => {
      const result = await subscriptionsService.reactivateSubscription(
        params.subscriptionId,
      );

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      if (variables.projectId) {
        queryClient.invalidateQueries({
          queryKey: ['project-subscription', variables.projectId],
        });
        invalidateProjectAccess(variables.projectId);
      }

      toast({
        title: 'Subscription Reactivated',
        description: 'The subscription has been reactivated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Reactivate Subscription',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Transition subscription to grace period
 */
export function useTransitionToGracePeriod() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      subscriptionId: string;
      projectId?: string;
      failureReason?: string;
    }): Promise<ProjectSubscription> => {
      const result = await subscriptionsService.transitionToGracePeriod(
        params.subscriptionId,
        params.failureReason,
      );

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      if (variables.projectId) {
        queryClient.invalidateQueries({
          queryKey: ['project-subscription', variables.projectId],
        });
        invalidateProjectAccess(variables.projectId);
      }

      toast({
        title: 'Grace Period Started',
        description: 'Subscription has been moved to grace period.',
        variant: 'destructive',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Start Grace Period',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

/**
 * Suspend a subscription
 */
export function useSuspendSubscription() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();

  return useMutation({
    mutationFn: async (params: {
      subscriptionId: string;
      projectId?: string;
      reason?: string;
    }): Promise<ProjectSubscription> => {
      const result = await subscriptionsService.suspendSubscription(
        params.subscriptionId,
        params.reason,
      );

      if (result.error || !result.data) {
        throw new Error(result.error || 'Operation failed');
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contractor-subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-stats'] });
      queryClient.invalidateQueries({
        queryKey: ['subscriptions-requiring-attention'],
      });

      if (variables.projectId) {
        queryClient.invalidateQueries({
          queryKey: ['project-subscription', variables.projectId],
        });
        invalidateProjectAccess(variables.projectId);
      }

      toast({
        title: 'Subscription Suspended',
        description: 'The subscription has been suspended.',
        variant: 'destructive',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Suspend Subscription',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}
