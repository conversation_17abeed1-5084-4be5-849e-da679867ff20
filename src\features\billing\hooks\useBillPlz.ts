import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { paymentsService } from '@/features/billing/services';
import type {
  CreatePaymentParams,
  PaymentStatusUpdate,
} from '@/features/billing/services';
import type { BillPlzBill, PaymentRecord } from '@/types/billing';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useInvalidateProjectAccess } from './useProjectAccess';

// ================================
// BILLPLZ QUERIES
// ================================

/**
 * Check payment status for a BillPlz bill and sync with subscription
 */
export function useCheckPaymentStatus(billId: string, paymentRecordId: string) {
  const invalidateProjectAccess = useInvalidateProjectAccess();
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['billplz-payment-status', billId, paymentRecordId],
    queryFn: async (): Promise<{
      bill: BillPlzBill;
      paymentRecord: PaymentRecord;
      subscriptionUpdated: boolean;
    }> => {
      if (!billId || !paymentRecordId) {
        throw new Error('Bill ID and Payment Record ID are required');
      }

      const result =
        await paymentsService.syncPaymentWithBillPlz(paymentRecordId);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to sync payment with BillPlz');
      }

      // Get payment URL for the bill
      const urlResult =
        await paymentsService.getBillPlzPaymentUrl(paymentRecordId);
      const mockBill: BillPlzBill = {
        id: billId,
        collection_id: '',
        url: urlResult.url || '',
        paid: result.data.paymentRecord.status === 'paid',
        state: result.data.paymentRecord.status === 'paid' ? 'paid' : 'open',
        amount: result.data.paymentRecord.amount || 0,
        paid_amount:
          result.data.paymentRecord.status === 'paid'
            ? result.data.paymentRecord.amount || 0
            : 0,
        due_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        email: '',
        mobile: null,
        name: '',
        reference_1_label: null,
        reference_1: null,
        reference_2_label: null,
        reference_2: null,
        redirect_url: null,
        callback_url: '',
        description: '',
        paid_at: result.data.paymentRecord.paid_at || null,
      };

      // Invalidate project access if access was restored
      if (result.data.accessRestored && user?.id) {
        // We'd need project info - for now just invalidate all
        invalidateProjectAccess(undefined, undefined, user.id);
      }

      return {
        bill: mockBill,
        paymentRecord: result.data.paymentRecord,
        subscriptionUpdated: result.data.subscriptionUpdated,
      };
    },
    enabled: !!billId && !!paymentRecordId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: (query) => {
      // Stop polling if payment is completed
      return query.state.data?.bill?.paid ? false : 60 * 1000; // Poll every minute if not paid
    },
  });
}

/**
 * Get BillPlz bill details
 */
export function useBillPlzBillDetails(billId: string) {
  return useQuery({
    queryKey: ['billplz-bill-details', billId],
    queryFn: async (): Promise<BillPlzBill> => {
      if (!billId) {
        throw new Error('Bill ID is required');
      }

      // Since we don't have direct BillPlz access in services, we'll return a mock
      // This would ideally be implemented in the payments service
      return {
        id: billId,
        collection_id: '',
        url: '',
        paid: false,
        state: 'open',
        amount: 0,
        paid_amount: 0,
        due_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        email: '',
        mobile: null,
        name: '',
        reference_1_label: null,
        reference_1: null,
        reference_2_label: null,
        reference_2: null,
        redirect_url: null,
        callback_url: '',
        description: '',
        paid_at: null,
      } as BillPlzBill;
    },
    enabled: !!billId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// ================================
// BILLPLZ MUTATIONS
// ================================

/**
 * Create BillPlz bill for subscription with access status updates
 */
export function useCreateBillForSubscription() {
  const queryClient = useQueryClient();
  const invalidateProjectAccess = useInvalidateProjectAccess();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      params: CreatePaymentParams,
    ): Promise<{
      bill: BillPlzBill;
      paymentRecord: PaymentRecord;
    }> => {
      const result = await paymentsService.createPaymentRecord(params);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to create payment record');
      }

      // Get payment URL
      const urlResult = await paymentsService.getBillPlzPaymentUrl(
        result.data.id,
      );

      const mockBill: BillPlzBill = {
        id: result.data.billplz_bill_id || '',
        collection_id: '',
        url: urlResult.url || '',
        paid: false,
        state: 'open',
        amount: result.data.amount || 0,
        paid_amount: 0,
        due_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        email: '',
        mobile: null,
        name: '',
        reference_1_label: null,
        reference_1: null,
        reference_2_label: null,
        reference_2: null,
        redirect_url: null,
        callback_url: '',
        description: '',
        paid_at: null,
      };

      return {
        bill: mockBill,
        paymentRecord: result.data,
      };
    },
    onSuccess: (data, variables) => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['contractor-subscriptions', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history', variables.subscriptionId],
      });
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });

      // Invalidate project access as status changed to pending payment
      invalidateProjectAccess(undefined, contractorId || undefined, user?.id);

      toast({
        title: 'Payment Link Created',
        description: 'BillPlz payment link has been created successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Payment Link',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Cancel BillPlz bill
 */
export function useCancelBillPlzBill() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      paymentRecordId,
      reason,
    }: {
      billId?: string;
      paymentRecordId: string;
      reason?: string;
    }): Promise<void> => {
      const updateParams: PaymentStatusUpdate = {
        paymentRecordId,
        status: 'cancelled' as const,
        failureReason: reason || 'Cancelled by user',
      };

      const result = await paymentsService.updatePaymentStatus(updateParams);

      if (result.error) {
        throw new Error(result.error);
      }
    },
    onSuccess: () => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate payment-related queries
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['billplz-bill-details'],
      });

      toast({
        title: 'Payment Cancelled',
        description: 'BillPlz payment has been cancelled successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Cancel Payment',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Retry BillPlz payment by creating a new bill for existing payment record
 */
export function useRetryBillPlzPayment() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      paymentRecordId,
    }: {
      paymentRecordId: string;
      contractorEmail?: string;
      contractorName?: string;
      callbackUrl?: string;
      redirectUrl?: string;
    }): Promise<{
      bill: BillPlzBill;
      paymentRecord: PaymentRecord;
    }> => {
      const result = await paymentsService.retryFailedPayment(paymentRecordId);

      if (result.error || !result.data) {
        throw new Error(result.error || 'Failed to retry payment');
      }

      const mockBill: BillPlzBill = {
        id: result.data.paymentRecord.billplz_bill_id || '',
        collection_id: '',
        url: result.data.billUrl || '',
        paid: false,
        state: 'open',
        amount: result.data.paymentRecord.amount || 0,
        paid_amount: 0,
        due_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        email: '',
        mobile: null,
        name: '',
        reference_1_label: null,
        reference_1: null,
        reference_2_label: null,
        reference_2: null,
        redirect_url: null,
        callback_url: '',
        description: '',
        paid_at: null,
      };

      return {
        bill: mockBill,
        paymentRecord: result.data.paymentRecord,
      };
    },
    onSuccess: () => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate payment-related queries
      queryClient.invalidateQueries({
        queryKey: ['contractor-payment-history', contractorId],
      });
      queryClient.invalidateQueries({
        queryKey: ['subscription-payment-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['retriable-payments', contractorId],
      });

      toast({
        title: 'Payment Retry Created',
        description: 'New BillPlz payment link has been created for retry.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Retry Payment',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}

// ================================
// UTILITY HOOKS
// ================================

/**
 * Get active BillPlz bills for contractor
 */
export function useActiveBillPlzBills() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['active-billplz-bills', contractorId],
    queryFn: async () => {
      if (!user || !contractorId) return [];

      const result =
        await paymentsService.getContractorPaymentHistory(contractorId);

      if (result.error) {
        throw new Error(result.error);
      }

      // Filter for pending payments with BillPlz bill IDs
      return result.data
        .filter(
          (payment) => payment.status === 'pending' && payment.billplz_bill_id,
        )
        .map((payment) => ({
          id: payment.id,
          billplz_bill_id: payment.billplz_bill_id,
          billplz_bill_url: '', // Not available in current schema
          amount: payment.amount,
          description: '', // Not available in current schema
          created_at: payment.created_at,
          subscription: {
            contractor_id: payment.subscription?.contractor_id,
            project: {
              name: payment.projectName,
              code: '', // Not available in current schema
            },
          },
        }));
    },
    enabled: !!user && !!contractorId,
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * BillPlz service health check
 */
export function useBillPlzHealthCheck() {
  return useQuery({
    queryKey: ['billplz-health'],
    queryFn: async (): Promise<{ healthy: boolean; responseTime: number }> => {
      const startTime = Date.now();

      try {
        // Since we don't have direct access to BillPlz health check in services,
        // we'll simulate a health check
        await new Promise((resolve) => setTimeout(resolve, 100));
        const responseTime = Date.now() - startTime;

        return {
          healthy: true,
          responseTime,
        };
      } catch {
        return {
          healthy: false,
          responseTime: Date.now() - startTime,
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once for health checks
  });
}
