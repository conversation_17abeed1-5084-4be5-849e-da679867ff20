'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useCompetentPersonsSearch,
  useCompetentPersonsStats,
} from '@/features/competent-persons';
import { usePermissions } from '@/hooks/use-permissions';
import type { CompetentPerson } from '@/types/competent-person';
import { format } from 'date-fns';
import {
  Award,
  Calendar,
  Edit,
  FileText,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  UserCheck,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';
import { AddCPModal } from './components/add-cp-modal';

/**
 * Utility functions for formatting data
 */
function useCompetentPersonUtils() {
  const t = useTranslations('cpList.fields');

  const formatDate = (dateString?: string) => {
    if (!dateString) return t('notSpecified');
    try {
      return format(new Date(dateString), 'dd/MM/yy');
    } catch {
      return t('invalidDate');
    }
  };

  const getCPTypeBadgeVariant = (cpType: string) => {
    switch (cpType) {
      case 'CP1':
        return 'default';
      case 'CP2':
        return 'secondary';
      case 'CP3':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const isExpired = (certExpDate?: string) => {
    return certExpDate ? new Date(certExpDate) < new Date() : false;
  };

  return { formatDate, getCPTypeBadgeVariant, isExpired };
}

/**
 * Summary Statistics Component
 */
function SummaryCards() {
  const t = useTranslations('cpList');
  const { data: competentPersons = [], isLoading } = useCompetentPersonsStats();

  const stats = useMemo(() => {
    const total = competentPersons.length;
    const byType = competentPersons.reduce(
      (acc, person) => {
        acc[person.cp_type] = (acc[person.cp_type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const expired = competentPersons.filter((person) =>
      person.cert_exp_date
        ? new Date(person.cert_exp_date) < new Date()
        : false,
    ).length;

    const totalPMAs = competentPersons.reduce(
      (acc, person) => acc + person.no_of_pma,
      0,
    );

    return { total, byType, expired, totalPMAs };
  }, [competentPersons]);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('totalCPs')}</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('byType')}</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {['CP1', 'CP2', 'CP3'].map((type) => (
              <div key={type} className="flex justify-between text-sm">
                <span>{type}:</span>
                <span className="font-medium">{stats.byType[type] || 0}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('expiredCerts')}
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-destructive">
            {stats.expired}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('totalPMAs')}
          </CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalPMAs}</div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Main CP List Page Component
 */
export default function CPListPage() {
  const t = useTranslations();
  const tFields = useTranslations('cpList.fields');
  const { isContractor } = usePermissions();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingPerson, setEditingPerson] = useState<CompetentPerson | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const { formatDate, getCPTypeBadgeVariant, isExpired } =
    useCompetentPersonUtils();

  // Use the new search-enabled hook
  const {
    data: searchResult,
    isLoading,
    error,
    isFetching,
  } = useCompetentPersonsSearch(searchQuery);

  // Extract data from search result
  const competentPersons = searchResult?.data || [];
  const totalCount = searchResult?.count || 0;

  const hasCompetentPersons = competentPersons && competentPersons.length > 0;

  // Only show to contractors
  if (!isContractor) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.accessRestricted.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('cpList.accessRestricted.description')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-6 bg-muted rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded w-5/6"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('cpList.errorLoading.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('cpList.errorLoading.description')}: {error.message}
            </p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{t('cpList.title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('cpList.description')}
          </p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t('cpList.addCP')}
        </Button>
      </div>

      {hasCompetentPersons ? (
        <>
          {/* Summary Statistics */}
          <SummaryCards />

          {/* Competent Persons Table */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                Competent Persons ({competentPersons.length}/{totalCount})
              </h2>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search competent persons..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {isFetching && (
                  <div className="absolute right-2.5 top-2.5">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                  </div>
                )}
              </div>
            </div>
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{tFields('name')}</TableHead>
                    <TableHead>{tFields('icNo')}</TableHead>
                    <TableHead>{tFields('cpType')}</TableHead>
                    <TableHead>{tFields('phone')}</TableHead>
                    <TableHead>{tFields('registrationNo')}</TableHead>
                    <TableHead>{tFields('certExpiry')}</TableHead>
                    <TableHead className="text-center">
                      {tFields('pmas')}
                    </TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {competentPersons.map((person) => (
                    <TableRow key={person.id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1.5 bg-primary/10 rounded-md">
                            <UserCheck className="h-4 w-4 text-primary" />
                          </div>
                          <span>{person.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {person.ic_no}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getCPTypeBadgeVariant(person.cp_type)}>
                          {person.cp_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {person.phone_no ? (
                          <div className="flex items-center space-x-2">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{person.phone_no}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cp_registeration_no ? (
                          <div className="flex items-center space-x-2">
                            <Award className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {person.cp_registeration_no}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {person.cert_exp_date ? (
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span
                              className={`text-sm ${
                                isExpired(person.cert_exp_date)
                                  ? 'text-destructive font-medium'
                                  : ''
                              }`}
                            >
                              {formatDate(person.cert_exp_date)}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            {tFields('notSpecified')}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <FileText className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">
                            {person.no_of_pma}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {person.cp_registeration_cert && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                window.open(
                                  person.cp_registeration_cert,
                                  '_blank',
                                )
                              }
                              title={tFields('viewCert')}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => setEditingPerson(person)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {tFields('editAction')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </div>
        </>
      ) : (
        // Empty state
        <Card>
          <CardContent className="text-center py-12">
            <UserCheck className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {t('cpList.noCompetentPersons.title')}
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              {t('cpList.noCompetentPersons.description')}
            </p>
            <Button onClick={() => setIsAddModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              {t('cpList.addFirstCP')}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Add/Edit CP Modal */}
      <AddCPModal
        open={isAddModalOpen || !!editingPerson}
        onOpenChange={(open) => {
          if (!open) {
            setIsAddModalOpen(false);
            setEditingPerson(null);
          }
        }}
        editingPerson={editingPerson}
      />
    </div>
  );
}
