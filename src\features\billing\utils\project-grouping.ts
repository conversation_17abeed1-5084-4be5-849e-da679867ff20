import type { PmaSubscriptionWithDetails } from '@/features/billing/types/pma-subscriptions';
import type {
  ContractorBillingData,
  ProjectWithPmas,
  BillingStats,
} from '@/features/billing/types/project-grouped-billing';
import type { SubscriptionWithAccess } from '@/types/billing';

/**
 * Groups PMA subscriptions by project and calculates aggregated billing data
 */
export function groupSubscriptionsByProject(
  subscriptions: PmaSubscriptionWithDetails[],
): ContractorBillingData {
  if (subscriptions.length === 0) {
    return {
      contractor_id: '',
      contractor_name: 'Unknown Contractor',
      projects: [],
      total_projects: 0,
      total_pmas: 0,
      total_active_pmas: 0,
      total_suspended_pmas: 0,
      total_monthly_amount: 0,
      has_urgent_payments: false,
      urgent_subscriptions_count: 0,
    };
  }

  // Get contractor info from first subscription
  const firstSubscription = subscriptions[0];
  const contractorId = firstSubscription.contractors?.id || '';
  const contractorName =
    firstSubscription.contractors?.name || 'Unknown Contractor';

  // Group subscriptions by project
  const projectGroups = new Map<
    string,
    {
      project_id: string;
      project_name: string;
      project_location?: string;
      subscriptions: PmaSubscriptionWithDetails[];
    }
  >();

  subscriptions.forEach((subscription) => {
    // Use PMA certificate's project info or fallback
    const projectId =
      subscription.pma_certificates?.project_id || 'unknown-project';
    const projectName = getProjectNameFromSubscription(subscription);
    const projectLocation =
      subscription.pma_certificates?.location || undefined;

    if (!projectGroups.has(projectId)) {
      projectGroups.set(projectId, {
        project_id: projectId,
        project_name: projectName,
        project_location: projectLocation,
        subscriptions: [],
      });
    }

    projectGroups.get(projectId)!.subscriptions.push(subscription);
  });

  // Transform grouped data into ProjectWithPmas
  const projects: ProjectWithPmas[] = Array.from(projectGroups.values()).map(
    (group) => {
      const projectSubscriptions = group.subscriptions;

      // Calculate project-level aggregations
      const activePmas = projectSubscriptions.filter(
        (s) => s.status === 'active',
      ).length;
      const suspendedPmas = projectSubscriptions.filter(
        (s) => s.status === 'suspended',
      ).length;
      const gracePeriodPmas = projectSubscriptions.filter(
        (s) => s.status === 'grace_period',
      ).length;
      const totalMonthlyAmount = projectSubscriptions.reduce(
        (sum, s) => sum + (s.amount || 15000),
        0,
      );

      // Check for urgent payments
      const hasUrgentPayments = projectSubscriptions.some((s) =>
        isSubscriptionUrgent(s),
      );

      // Find next billing date
      const nextBillingDates = projectSubscriptions
        .map((s) => s.next_billing_date)
        .filter(Boolean)
        .sort();
      const nextBillingDate = nextBillingDates[0] || undefined;

      return {
        project_id: group.project_id,
        project_name: group.project_name,
        project_location: group.project_location,
        pma_count: projectSubscriptions.length,
        active_pmas: activePmas,
        suspended_pmas: suspendedPmas,
        grace_period_pmas: gracePeriodPmas,
        total_monthly_amount: totalMonthlyAmount,
        pma_subscriptions: projectSubscriptions.map(
          convertToSubscriptionWithAccess,
        ),
        has_urgent_payments: hasUrgentPayments,
        next_billing_date: nextBillingDate,
      };
    },
  );

  // Sort projects by urgency first, then by name
  projects.sort((a, b) => {
    if (a.has_urgent_payments && !b.has_urgent_payments) return -1;
    if (!a.has_urgent_payments && b.has_urgent_payments) return 1;
    return a.project_name.localeCompare(b.project_name);
  });

  // Calculate contractor-level aggregations
  const totalPmas = subscriptions.length;
  const totalActivePmas = subscriptions.filter(
    (s) => s.status === 'active',
  ).length;
  const totalSuspendedPmas = subscriptions.filter(
    (s) => s.status === 'suspended',
  ).length;
  const totalMonthlyAmount = subscriptions.reduce(
    (sum, s) => sum + (s.amount || 15000),
    0,
  );
  const hasUrgentPayments = subscriptions.some((s) => isSubscriptionUrgent(s));
  const urgentSubscriptionsCount = subscriptions.filter((s) =>
    isSubscriptionUrgent(s),
  ).length;

  return {
    contractor_id: contractorId,
    contractor_name: contractorName,
    projects,
    total_projects: projects.length,
    total_pmas: totalPmas,
    total_active_pmas: totalActivePmas,
    total_suspended_pmas: totalSuspendedPmas,
    total_monthly_amount: totalMonthlyAmount,
    has_urgent_payments: hasUrgentPayments,
    urgent_subscriptions_count: urgentSubscriptionsCount,
  };
}

/**
 * Calculates overall billing statistics
 */
export function calculateBillingStats(
  contractorData: ContractorBillingData,
): BillingStats {
  return {
    total_subscriptions: contractorData.total_pmas,
    total_projects: contractorData.total_projects,
    total_monthly_amount: contractorData.total_monthly_amount,
    active_subscriptions: contractorData.total_active_pmas,
    suspended_subscriptions: contractorData.total_suspended_pmas,
    grace_period_subscriptions: contractorData.projects.reduce(
      (sum, p) => sum + p.grace_period_pmas,
      0,
    ),
    urgent_payments_count: contractorData.urgent_subscriptions_count,
  };
}

/**
 * Checks if a subscription requires urgent attention
 */
function isSubscriptionUrgent(
  subscription: PmaSubscriptionWithDetails,
): boolean {
  // Grace period ending soon (within 24 hours)
  if (subscription.grace_period_ends) {
    const gracePeriodEnd = new Date(subscription.grace_period_ends);
    const now = new Date();
    const hoursUntilEnd =
      (gracePeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntilEnd <= 24 && hoursUntilEnd > 0) {
      return true;
    }
  }

  // Suspended status
  return subscription.status === 'suspended';
}

/**
 * Converts PmaSubscriptionWithDetails to SubscriptionWithAccess
 */
function convertToSubscriptionWithAccess(
  pmaSubscription: PmaSubscriptionWithDetails,
): SubscriptionWithAccess {
  // Calculate access status first
  const accessAllowed =
    pmaSubscription.access_allowed ?? pmaSubscription.status === 'active';

  // Map PMA subscription fields to project subscription fields
  const projectSubscription = {
    id: pmaSubscription.id,
    project_id: pmaSubscription.pma_certificates?.project_id || '',
    contractor_id: pmaSubscription.contractor_id,
    amount: pmaSubscription.amount || 15000, // Default amount in cents
    currency: pmaSubscription.currency || 'MYR',
    status: pmaSubscription.status as
      | 'active'
      | 'suspended'
      | 'grace_period'
      | 'cancelled'
      | 'pending_payment',
    billing_cycle: 'monthly' as const,
    next_billing_date: pmaSubscription.next_billing_date,
    current_period_start: pmaSubscription.created_at,
    current_period_end: pmaSubscription.next_billing_date,
    grace_period_ends: pmaSubscription.grace_period_ends,
    suspended_at:
      pmaSubscription.status === 'suspended' ? new Date().toISOString() : null,
    cancelled_at:
      pmaSubscription.status === 'cancelled' ? new Date().toISOString() : null,
    access_allowed: accessAllowed,
    created_at: pmaSubscription.created_at,
    updated_at: pmaSubscription.updated_at,
  };

  // Calculate additional access status
  const isInGracePeriod =
    pmaSubscription.status === 'grace_period' ||
    Boolean(pmaSubscription.grace_period_ends);

  let daysUntilSuspension: number | undefined;
  if (pmaSubscription.grace_period_ends) {
    const gracePeriodEnd = new Date(pmaSubscription.grace_period_ends);
    const now = new Date();
    const diffTime = gracePeriodEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    daysUntilSuspension = diffDays > 0 ? diffDays : 0;
  }

  return {
    ...projectSubscription,
    accessAllowed,
    isInGracePeriod,
    daysUntilSuspension,
  };
}

/**
 * Extracts project name from subscription data
 */
function getProjectNameFromSubscription(
  subscription: PmaSubscriptionWithDetails,
): string {
  // Try to get from PMA certificate first
  if (subscription.pma_certificates?.project_id) {
    // If we had project data in the subscription, we'd use it here
    // For now, use a fallback based on PMA number or location
    const pmaNumber = subscription.pma_certificates.pma_number;
    const location = subscription.pma_certificates.location;

    if (pmaNumber) {
      return `Project ${pmaNumber}`;
    }

    if (location) {
      return `Project at ${location}`;
    }
  }

  // Fallback to generic name
  return `Project ${subscription.pma_certificates?.project_id || 'Unknown'}`;
}
