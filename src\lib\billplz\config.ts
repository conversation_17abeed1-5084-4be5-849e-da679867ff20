/**
 * BillPlz Payment Gateway Configuration
 * Environment-based configuration for sandbox and production modes
 */

// ================================
// ENVIRONMENT VALIDATION
// ================================

function getRequiredEnvVar(name: string): string {
  // Prevent client-side access to server environment variables
  if (typeof window !== 'undefined') {
    throw new Error(
      `Cannot access server environment variable ${name} on client side. BillPlz services must only be used server-side.`,
    );
  }

  const value = process.env[name];
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

function getOptionalEnvVar(name: string, defaultValue: string = ''): string {
  // Prevent client-side access to server environment variables
  if (typeof window !== 'undefined') {
    return defaultValue;
  }

  return process.env[name] || defaultValue;
}

// ================================
// BILLPLZ CONFIGURATION INTERFACE
// ================================

export interface BillPlzConfig {
  apiKey: string;
  collectionId: string;
  xSignatureKey: string;
  sandboxMode: boolean;
  webhookUrl?: string;
  apiVersion: string;
  baseUrl: string;
  timeout: number;
}

// ================================
// ENVIRONMENT-BASED CONFIGURATION
// ================================

/**
 * Get BillPlz configuration based on current environment
 * SERVER-SIDE ONLY - Will throw error if called from client
 */
export function getBillPlzConfig(): BillPlzConfig {
  // Ensure this only runs on server side
  if (typeof window !== 'undefined') {
    throw new Error(
      'getBillPlzConfig() can only be called server-side. Use API routes for client-side billing operations.',
    );
  }

  const sandboxMode =
    getOptionalEnvVar('BILLPLZ_SANDBOX_MODE', 'true') === 'true';
  const apiVersion = getOptionalEnvVar('BILLPLZ_API_VERSION', 'v4');

  // Validate required environment variables
  const apiKey = getRequiredEnvVar('BILLPLZ_API_KEY');
  const collectionId = getRequiredEnvVar('BILLPLZ_COLLECTION_ID');
  const xSignatureKey = getRequiredEnvVar('BILLPLZ_X_SIGNATURE_KEY');

  // Optional webhook URL
  const webhookUrl = getOptionalEnvVar('BILLPLZ_WEBHOOK_URL');

  return {
    apiKey,
    collectionId,
    xSignatureKey,
    sandboxMode,
    webhookUrl: webhookUrl || undefined,
    apiVersion,
    baseUrl: sandboxMode
      ? 'https://www.billplz-sandbox.com/api'
      : 'https://www.billplz.com/api',
    timeout: 30000, // 30 seconds timeout
  };
}

// ================================
// API ENDPOINTS CONFIGURATION
// ================================

export const BILLPLZ_ENDPOINTS = {
  // Bills endpoints
  BILLS: '/bills',
  BILL_BY_ID: (billId: string) => `/bills/${billId}`,

  // Collections endpoints
  COLLECTIONS: '/collections',
  COLLECTION_BY_ID: (collectionId: string) => `/collections/${collectionId}`,

  // Payment methods endpoints
  PAYMENT_METHODS: '/payment_methods',

  // Transaction endpoints
  TRANSACTIONS: (billId: string) => `/bills/${billId}/transactions`,
} as const;

// ================================
// DEFAULT CONFIGURATION VALUES
// ================================

export const BILLPLZ_DEFAULTS = {
  // Currency
  CURRENCY: 'MYR',

  // Default collection name for new contractors
  DEFAULT_COLLECTION_TITLE: 'SimPLE Lift Management',
  DEFAULT_COLLECTION_LOGO: '', // Can be set to company logo URL

  // Payment settings
  DEFAULT_DUE_DAYS: 7, // 7 days from creation
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000, // 1 second base delay

  // Amount limits (in MYR)
  MIN_AMOUNT: 1.0,
  MAX_AMOUNT: 50000.0,

  // Bill reference format
  BILL_REFERENCE_PREFIX: 'SIMPLE',

  // Webhook retry settings
  WEBHOOK_MAX_RETRIES: 5,
  WEBHOOK_TIMEOUT_MS: 10000, // 10 seconds
} as const;

// ================================
// RATE LIMITING CONFIGURATION
// ================================

export const RATE_LIMIT_CONFIG = {
  // BillPlz rate limits (conservative estimates)
  MAX_REQUESTS_PER_MINUTE: 60,
  MAX_REQUESTS_PER_HOUR: 1000,

  // Retry configuration
  MAX_RETRIES: 3,
  BASE_DELAY_MS: 1000,
  MAX_DELAY_MS: 30000,
  BACKOFF_MULTIPLIER: 2,

  // Circuit breaker settings
  FAILURE_THRESHOLD: 5,
  RECOVERY_TIMEOUT_MS: 60000, // 1 minute
} as const;

// ================================
// LOGGING CONFIGURATION
// ================================

export const LOGGING_CONFIG = {
  // What to log in different environments
  LOG_REQUESTS: process.env.NODE_ENV === 'development',
  LOG_RESPONSES: process.env.NODE_ENV === 'development',
  LOG_ERRORS: true,

  // Sensitive data masking
  MASK_API_KEY: true,
  MASK_SIGNATURE_KEY: true,
  MASK_WEBHOOK_DATA: false,

  // Log levels
  REQUEST_LOG_LEVEL: 'info' as const,
  ERROR_LOG_LEVEL: 'error' as const,
  DEBUG_LOG_LEVEL: 'debug' as const,
} as const;

// ================================
// VALIDATION HELPERS
// ================================

/**
 * Validate BillPlz configuration on startup
 */
export function validateBillPlzConfig(config: BillPlzConfig): void {
  const errors: string[] = [];

  // API Key validation
  if (!config.apiKey || config.apiKey.length < 10) {
    errors.push('Invalid API key: must be at least 10 characters');
  }

  // Collection ID validation
  if (!config.collectionId || !/^[a-zA-Z0-9_-]+$/.test(config.collectionId)) {
    errors.push(
      'Invalid collection ID: must contain only alphanumeric characters, underscores, and hyphens',
    );
  }

  // X-Signature Key validation
  if (!config.xSignatureKey || config.xSignatureKey.length < 10) {
    errors.push('Invalid X-Signature Key: must be at least 10 characters');
  }

  // Webhook URL validation (if provided)
  if (config.webhookUrl) {
    try {
      new URL(config.webhookUrl);
    } catch {
      errors.push('Invalid webhook URL: must be a valid HTTPS URL');
    }

    if (config.webhookUrl && !config.webhookUrl.startsWith('https://')) {
      errors.push('Webhook URL must use HTTPS');
    }
  }

  // API Version validation
  if (!config.apiVersion || !['v3', 'v4'].includes(config.apiVersion)) {
    errors.push('Invalid API version: must be v3 or v4');
  }

  if (errors.length > 0) {
    throw new Error(
      `BillPlz configuration errors:\n${errors.map((e) => `- ${e}`).join('\n')}`,
    );
  }
}

// ================================
// CONFIGURATION INSTANCE
// ================================

let configInstance: BillPlzConfig | null = null;

/**
 * Get singleton BillPlz configuration instance
 * SERVER-SIDE ONLY - Validates configuration on first access
 */
export function getBillPlzConfigSingleton(): BillPlzConfig {
  // Ensure this only runs on server side
  if (typeof window !== 'undefined') {
    throw new Error(
      'getBillPlzConfigSingleton() can only be called server-side. Use API routes for client-side billing operations.',
    );
  }

  if (!configInstance) {
    configInstance = getBillPlzConfig();

    // Validate configuration in non-test environments
    if (process.env.NODE_ENV !== 'test') {
      validateBillPlzConfig(configInstance);
    }

    // Log configuration status (without sensitive data)
    if (LOGGING_CONFIG.LOG_REQUESTS) {
      console.info('BillPlz configuration initialized:', {
        sandboxMode: configInstance.sandboxMode,
        apiVersion: configInstance.apiVersion,
        baseUrl: configInstance.baseUrl,
        hasWebhookUrl: !!configInstance.webhookUrl,
        timeout: configInstance.timeout,
      });
    }
  }

  return configInstance;
}

// ================================
// ENVIRONMENT HELPERS
// ================================

/**
 * Check if running in sandbox mode
 */
export function isSandboxMode(): boolean {
  return getBillPlzConfigSingleton().sandboxMode;
}

/**
 * Get BillPlz API base URL
 */
export function getApiBaseUrl(): string {
  return getBillPlzConfigSingleton().baseUrl;
}

/**
 * Get full API endpoint URL
 */
export function getApiEndpoint(endpoint: string): string {
  const config = getBillPlzConfigSingleton();
  return `${config.baseUrl}/${config.apiVersion}${endpoint}`;
}

/**
 * Get masked API key for logging purposes
 */
export function getMaskedApiKey(): string {
  const config = getBillPlzConfigSingleton();
  if (!LOGGING_CONFIG.MASK_API_KEY) {
    return config.apiKey;
  }
  return (
    config.apiKey.slice(0, 4) +
    '*'.repeat(Math.max(0, config.apiKey.length - 8)) +
    config.apiKey.slice(-4)
  );
}

// ================================
// EXPORTS
// ================================

// Export the main configuration getter as default
export default getBillPlzConfigSingleton;
