-- ================================
-- FIX RLS POLICIES FOR PROJECT ACCESS CONTROL
-- ================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view projects they have access to" ON projects;
DROP POLICY IF EXISTS "Contractors can create projects" ON projects;
DROP POLICY IF EXISTS "Users can update projects they have access to" ON projects;
DROP POLICY IF EXISTS "Users can view project members for accessible projects" ON project_users;
DROP POLICY IF EXISTS "Project admins can manage project members" ON project_users;

-- Disable <PERSON><PERSON> temporarily to fix issues
ALTER TABLE projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE project_users DISABLE ROW LEVEL SECURITY;

-- <PERSON>reate updated function that works with Supabase auth
CREATE OR REPLACE FUNCTION user_has_project_access(user_id_param uuid, project_id_param uuid)
R<PERSON>URNS boolean AS $$
DECLARE
    user_record RECORD;
    has_access boolean := false;
BEGIN
    -- Get user information from users table
    SELECT user_role, admin_access_mode, monitoring_state, contractor_id
    INTO user_record
    FROM users
    WHERE id = user_id_param AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- Admin users with project-level access can see all projects
    IF user_record.user_role = 'admin' AND user_record.admin_access_mode = 'project' THEN
        RETURN true;
    END IF;
    
    -- Admin users with state-level access can see projects in their monitoring state
    IF user_record.user_role = 'admin' AND user_record.admin_access_mode = 'state' THEN
        SELECT EXISTS (
            SELECT 1 FROM projects 
            WHERE id = project_id_param 
            AND state = user_record.monitoring_state
            AND deleted_at IS NULL
        ) INTO has_access;
        RETURN has_access;
    END IF;
    
    -- For contractors and viewers, check project_users table
    SELECT EXISTS (
        SELECT 1 FROM project_users pu
        WHERE pu.user_id = user_id_param
        AND pu.project_id = project_id_param
        AND pu.is_active = true
        AND pu.status = 'accepted'
        AND pu.deleted_at IS NULL
    ) INTO has_access;
    
    RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Re-enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_users ENABLE ROW LEVEL SECURITY;

-- Create new policies that work with the auth system
CREATE POLICY "Enable read access for authenticated users based on project access" ON projects
    FOR SELECT
    TO authenticated
    USING (user_has_project_access(auth.uid(), id));

CREATE POLICY "Enable insert for contractors" ON projects
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'contractor'
            AND deleted_at IS NULL
        )
    );

CREATE POLICY "Enable update for project admins and system admins" ON projects
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'admin'
            AND deleted_at IS NULL
        )
        OR
        EXISTS (
            SELECT 1 FROM project_users pu
            WHERE pu.user_id = auth.uid()
            AND pu.project_id = id
            AND pu.role = 'admin'
            AND pu.is_active = true
            AND pu.status = 'accepted'
            AND pu.deleted_at IS NULL
        )
    );

-- Project users policies
CREATE POLICY "Enable read access for project members" ON project_users
    FOR SELECT
    TO authenticated
    USING (user_has_project_access(auth.uid(), project_id));

CREATE POLICY "Enable all operations for project admins and system admins" ON project_users
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_role = 'admin'
            AND deleted_at IS NULL
        )
        OR
        EXISTS (
            SELECT 1 FROM project_users pu
            WHERE pu.user_id = auth.uid()
            AND pu.project_id = project_id
            AND pu.role = 'admin'
            AND pu.is_active = true
            AND pu.status = 'accepted'
            AND pu.deleted_at IS NULL
        )
    );