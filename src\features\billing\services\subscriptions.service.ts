import { supabase } from '@/lib/supabase';
import type {
  ProjectSubscription,
  ProjectSubscriptionInsert,
  ProjectSubscriptionUpdate,
  SubscriptionStatus,
  SubscriptionWithAccess,
} from '@/types/billing';
import {
  BILLING_CONSTANTS,
  calculateGracePeriodEnd,
  calculateNextBillingDate,
  checkProjectAccess,
  getDaysRemainingInGracePeriod,
} from '@/types/billing';

export interface CreateSubscriptionParams {
  projectId: string;
  contractorId: string;
  status?: SubscriptionStatus;
  nextBillingDate?: Date;
}

export interface UpdateSubscriptionAccessParams {
  subscriptionId: string;
  status: SubscriptionStatus;
  gracePeriodEnds?: Date | null;
  failureReason?: string | null;
}

export interface SubscriptionFilters {
  contractorId?: string;
  projectId?: string;
  status?: SubscriptionStatus;
  accessAllowed?: boolean;
}

export class SubscriptionsService {
  async createSubscriptionForProject(
    params: CreateSubscriptionParams,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      const {
        projectId,
        contractorId,
        status = 'active',
        nextBillingDate,
      } = params;

      // Check if subscription already exists for this project
      const existingSubscription = await this.getByProjectId(projectId);
      if (existingSubscription.data) {
        return {
          data: null,
          error: 'Subscription already exists for this project',
        };
      }

      // Prepare subscription data
      const subscriptionData: ProjectSubscriptionInsert = {
        project_id: projectId,
        contractor_id: contractorId,
        status,
        amount: BILLING_CONSTANTS.MONTHLY_SUBSCRIPTION_AMOUNT,
        next_billing_date: (
          nextBillingDate || calculateNextBillingDate()
        ).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Set grace period end if status is grace_period
      if (status === 'grace_period') {
        subscriptionData.grace_period_ends =
          calculateGracePeriodEnd().toISOString();
      }

      const { data, error } = await supabase
        .from('project_subscriptions')
        .insert(subscriptionData)
        .select()
        .single();

      if (error) {
        console.error('Failed to create subscription:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Create subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async updateSubscriptionAccess(
    params: UpdateSubscriptionAccessParams,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      const { subscriptionId, status, gracePeriodEnds } = params;

      // Prepare update data with status transition logic
      const updateData: ProjectSubscriptionUpdate = {
        status,
        updated_at: new Date().toISOString(),
        // Note: failure_reason field does not exist in current schema
      };

      // Handle status-specific logic
      switch (status) {
        case 'active':
          // Clear grace period and failure reason on activation
          updateData.grace_period_ends = null;
          // Note: failure_reason field does not exist in current schema
          break;

        case 'grace_period':
          // Set grace period end date
          updateData.grace_period_ends = (
            gracePeriodEnds || calculateGracePeriodEnd()
          ).toISOString();
          break;

        case 'suspended':
        case 'cancelled':
          // Clear grace period on final status
          updateData.grace_period_ends = null;
          break;

        case 'pending_payment':
          // Update next billing date if payment is pending
          updateData.next_billing_date =
            calculateNextBillingDate().toISOString();
          break;
      }

      const { data, error } = await supabase
        .from('project_subscriptions')
        .update(updateData)
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Failed to update subscription access:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Update subscription access error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getSubscriptionWithAccess(
    subscriptionId: string,
  ): Promise<{ data: SubscriptionWithAccess | null; error: string | null }> {
    try {
      const { data: subscription, error } = await supabase
        .from('project_subscriptions')
        .select('*')
        .eq('id', subscriptionId)
        .single();

      if (error) {
        console.error('Failed to get subscription:', error);
        return { data: null, error: error.message };
      }

      // Calculate access status
      const accessAllowed = checkProjectAccess(subscription);
      const isInGracePeriod = subscription.status === 'grace_period';
      const daysUntilSuspension = isInGracePeriod
        ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
        : undefined;

      const subscriptionWithAccess: SubscriptionWithAccess = {
        ...subscription,
        accessAllowed,
        isInGracePeriod,
        daysUntilSuspension: daysUntilSuspension ?? undefined,
      };

      return { data: subscriptionWithAccess, error: null };
    } catch (error) {
      console.error('Get subscription with access error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getByProjectId(
    projectId: string,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('project_subscriptions')
        .select('*')
        .eq('project_id', projectId)
        .single();

      if (error) {
        // Not found is not an error for this function
        if (error.code === 'PGRST116') {
          return { data: null, error: null };
        }
        console.error('Failed to get subscription by project:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Get subscription by project error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getByContractorId(
    contractorId: string,
  ): Promise<{ data: ProjectSubscription[]; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('project_subscriptions')
        .select('*')
        .eq('contractor_id', contractorId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to get subscriptions by contractor:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get subscriptions by contractor error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getAllWithFilters(
    filters: SubscriptionFilters,
  ): Promise<{ data: SubscriptionWithAccess[]; error: string | null }> {
    try {
      let query = supabase
        .from('project_subscriptions')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.contractorId) {
        query = query.eq('contractor_id', filters.contractorId);
      }
      if (filters.projectId) {
        query = query.eq('project_id', filters.projectId);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      const { data: subscriptions, error } = await query;

      if (error) {
        console.error('Failed to get subscriptions with filters:', error);
        return { data: [], error: error.message };
      }

      // Calculate access status for each subscription
      const subscriptionsWithAccess: SubscriptionWithAccess[] = (
        subscriptions || []
      ).map((subscription) => {
        const accessAllowed = checkProjectAccess(subscription);
        const isInGracePeriod = subscription.status === 'grace_period';
        const daysUntilSuspension = isInGracePeriod
          ? getDaysRemainingInGracePeriod(subscription.grace_period_ends)
          : undefined;

        return {
          ...subscription,
          accessAllowed,
          isInGracePeriod,
          daysUntilSuspension: daysUntilSuspension ?? undefined,
        };
      });

      // Apply access filter if specified
      if (filters.accessAllowed !== undefined) {
        const filteredSubscriptions = subscriptionsWithAccess.filter(
          (sub) => sub.accessAllowed === filters.accessAllowed,
        );
        return { data: filteredSubscriptions, error: null };
      }

      return { data: subscriptionsWithAccess, error: null };
    } catch (error) {
      console.error('Get subscriptions with filters error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async transitionToGracePeriod(
    subscriptionId: string,
    failureReason?: string,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    return this.updateSubscriptionAccess({
      subscriptionId,
      status: 'grace_period',
      gracePeriodEnds: calculateGracePeriodEnd(),
      failureReason: failureReason || 'Payment failed',
    });
  }

  async suspendSubscription(
    subscriptionId: string,
    reason?: string,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    return this.updateSubscriptionAccess({
      subscriptionId,
      status: 'suspended',
      gracePeriodEnds: null,
      failureReason: reason || 'Grace period expired',
    });
  }

  async reactivateSubscription(
    subscriptionId: string,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      // Update next billing date when reactivating
      const nextBillingDate = calculateNextBillingDate();

      const updateData: ProjectSubscriptionUpdate = {
        status: 'active',
        grace_period_ends: null,
        // Note: failure_reason field does not exist in current schema
        next_billing_date: nextBillingDate.toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('project_subscriptions')
        .update(updateData)
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Failed to reactivate subscription:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Reactivate subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async updateNextBillingDate(
    subscriptionId: string,
    nextBillingDate: Date,
  ): Promise<{ data: ProjectSubscription | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('project_subscriptions')
        .update({
          next_billing_date: nextBillingDate.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Failed to update next billing date:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Update next billing date error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async deleteSubscription(
    subscriptionId: string,
  ): Promise<{ success: boolean; error: string | null }> {
    try {
      const { error } = await supabase
        .from('project_subscriptions')
        .delete()
        .eq('id', subscriptionId);

      if (error) {
        console.error('Failed to delete subscription:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Delete subscription error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getSubscriptionsDueForBilling(
    targetDate?: Date,
  ): Promise<{ data: ProjectSubscription[]; error: string | null }> {
    try {
      const billingDate = targetDate || new Date();

      const { data, error } = await supabase
        .from('project_subscriptions')
        .select('*')
        .eq('status', 'active')
        .lte('next_billing_date', billingDate.toISOString())
        .order('next_billing_date', { ascending: true });

      if (error) {
        console.error('Failed to get subscriptions due for billing:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get subscriptions due for billing error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getSubscriptionsInGracePeriod(): Promise<{
    data: SubscriptionWithAccess[];
    error: string | null;
  }> {
    try {
      const { data: subscriptions, error } = await supabase
        .from('project_subscriptions')
        .select('*')
        .eq('status', 'grace_period')
        .not('grace_period_ends', 'is', null)
        .order('grace_period_ends', { ascending: true });

      if (error) {
        console.error('Failed to get subscriptions in grace period:', error);
        return { data: [], error: error.message };
      }

      // Filter out expired grace periods and calculate remaining days
      const now = new Date();
      const validGracePeriodSubscriptions: SubscriptionWithAccess[] = (
        subscriptions || []
      )
        .filter((sub) => {
          if (!sub.grace_period_ends) return false;
          return new Date(sub.grace_period_ends) > now;
        })
        .map((subscription) => ({
          ...subscription,
          accessAllowed: true, // Grace period allows access
          isInGracePeriod: true,
          daysUntilSuspension:
            getDaysRemainingInGracePeriod(subscription.grace_period_ends) ?? 0,
        }));

      return { data: validGracePeriodSubscriptions, error: null };
    } catch (error) {
      console.error('Get subscriptions in grace period error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export const subscriptionsService = new SubscriptionsService();
