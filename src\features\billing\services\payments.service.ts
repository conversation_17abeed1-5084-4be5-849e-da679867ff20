import { BillPlzClient } from '@/lib/billplz';
import { supabase } from '@/lib/supabase';
import type {
  BillPlzBill,
  PaymentRecord,
  PaymentRecordInsert,
  PaymentRecordJoined,
  PaymentRecordUpdate,
  PaymentRecordWithSubscription,
  PaymentStatus,
} from '@/types/billing';
import { convertCentsToMyr, convertMyrToCents } from '@/types/billing';
import type { Json } from '@/types/database';
import { subscriptionsService } from './subscriptions.service';
// Note: accessControlService import removed as not used

export interface CreatePaymentParams {
  subscriptionId: string;
  amount: number; // in MYR
  contractorEmail: string;
  contractorName: string;
  contractorMobile?: string;
  description: string;
  redirectUrl?: string;
  reference1?: string;
  reference2?: string;
}

export interface PaymentStatusUpdate {
  paymentRecordId: string;
  status: PaymentStatus;
  billPlzData?: BillPlzBill;
  paidAmount?: number; // in cents from BillPlz
  paidAt?: string;
  failureReason?: string;
}

export interface PaymentSyncResult {
  paymentRecord: PaymentRecord;
  subscriptionUpdated: boolean;
  accessRestored: boolean;
}

export class PaymentsService {
  private billPlzClient: BillPlzClient | null = null;

  constructor() {
    // Don't initialize BillPlz client in constructor to prevent client-side errors
    // It will be lazily initialized when first needed (server-side only)
  }

  private getBillPlzClient(): BillPlzClient {
    // Ensure this only runs on server side
    if (typeof window !== 'undefined') {
      throw new Error(
        'PaymentsService can only be used server-side. Use API routes for client-side billing operations.',
      );
    }

    if (!this.billPlzClient) {
      this.billPlzClient = new BillPlzClient();
    }
    return this.billPlzClient;
  }

  async createPaymentRecord(
    params: CreatePaymentParams,
  ): Promise<{ data: PaymentRecord | null; error: string | null }> {
    try {
      const {
        subscriptionId,
        amount,
        contractorEmail,
        contractorName,
        contractorMobile,
        description,
        redirectUrl,
        // reference1 and reference2 handled by BillPlz client
      } = params;

      // Verify subscription exists
      const subscriptionResult =
        await subscriptionsService.getSubscriptionWithAccess(subscriptionId);
      if (subscriptionResult.error || !subscriptionResult.data) {
        return {
          data: null,
          error: subscriptionResult.error || 'Subscription not found',
        };
      }

      const subscription = subscriptionResult.data;

      // Create BillPlz bill
      const billResult = await this.getBillPlzClient().createBill({
        contractorEmail,
        contractorName,
        contractorMobile,
        amount, // BillPlz client handles MYR to cents conversion
        description,
        projectId: subscription.project_id,
        subscriptionId,
        callbackUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/billing/webhook`,
        redirectUrl:
          redirectUrl ||
          `${process.env.NEXT_PUBLIC_APP_URL}/projects/${subscription.project_id}`,
      });

      if (!billResult.success || !billResult.data) {
        return {
          data: null,
          error: billResult.error || 'Failed to create BillPlz bill',
        };
      }

      const bill = billResult.data;

      // Create payment record in database
      const paymentData: PaymentRecordInsert = {
        subscription_id: subscriptionId,
        billplz_bill_id: bill.id,
        amount: convertMyrToCents(amount),
        status: 'pending',
        billplz_response: bill as unknown as Json, // BillPlz response as Json for Supabase
        created_at: new Date().toISOString(),
      };

      const { data: paymentRecord, error } = await supabase
        .from('payment_records')
        .insert(paymentData)
        .select()
        .single();

      if (error) {
        console.error('Failed to create payment record:', error);
        // Try to delete the BillPlz bill if database insert fails
        await this.getBillPlzClient().deleteBill(bill.id);
        return { data: null, error: error.message };
      }

      // Update subscription status to pending_payment
      await subscriptionsService.updateSubscriptionAccess({
        subscriptionId,
        status: 'pending_payment',
      });

      return { data: paymentRecord, error: null };
    } catch (error) {
      console.error('Create payment record error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async updatePaymentStatus(
    params: PaymentStatusUpdate,
  ): Promise<{ data: PaymentSyncResult | null; error: string | null }> {
    try {
      const {
        paymentRecordId,
        status,
        billPlzData,
        // paidAmount not stored in current schema
        paidAt,
        failureReason,
      } = params;

      // Get current payment record
      const { data: currentPayment, error: fetchError } = await supabase
        .from('payment_records')
        .select('*, subscription_id')
        .eq('id', paymentRecordId)
        .single();

      if (fetchError || !currentPayment) {
        return {
          data: null,
          error: fetchError?.message || 'Payment record not found',
        };
      }

      // Prepare payment update data
      const updateData: PaymentRecordUpdate = {
        status,
      };

      if (billPlzData) {
        updateData.billplz_response = billPlzData as unknown as Json;
      }

      if (paidAt) {
        updateData.paid_at = paidAt;
      }

      if (failureReason) {
        updateData.failure_reason = failureReason;
      }

      // Update payment record
      const { data: updatedPayment, error: updateError } = await supabase
        .from('payment_records')
        .update(updateData)
        .eq('id', paymentRecordId)
        .select()
        .single();

      if (updateError || !updatedPayment) {
        return {
          data: null,
          error: updateError?.message || 'Failed to update payment record',
        };
      }

      // Handle subscription status updates based on payment status
      let subscriptionUpdated = false;
      let accessRestored = false;

      if (status === 'paid') {
        // Payment successful - reactivate subscription
        const reactivateResult =
          await subscriptionsService.reactivateSubscription(
            currentPayment.subscription_id,
          );

        if (reactivateResult.data) {
          subscriptionUpdated = true;
          accessRestored = true;
        }
      } else if (status === 'failed') {
        // Payment failed - transition to grace period
        const gracePeriodResult =
          await subscriptionsService.transitionToGracePeriod(
            currentPayment.subscription_id,
            failureReason || 'Payment failed',
          );

        if (gracePeriodResult.data) {
          subscriptionUpdated = true;
        }
      }

      return {
        data: {
          paymentRecord: updatedPayment,
          subscriptionUpdated,
          accessRestored,
        },
        error: null,
      };
    } catch (error) {
      console.error('Update payment status error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async syncPaymentWithBillPlz(paymentRecordId: string): Promise<{
    data: PaymentSyncResult | null;
    error: string | null;
  }> {
    try {
      // Get payment record
      const { data: paymentRecord, error: fetchError } = await supabase
        .from('payment_records')
        .select('*')
        .eq('id', paymentRecordId)
        .single();

      if (fetchError || !paymentRecord) {
        return {
          data: null,
          error: fetchError?.message || 'Payment record not found',
        };
      }

      // Get latest status from BillPlz
      if (!paymentRecord.billplz_bill_id) {
        return {
          data: null,
          error: 'Payment record has no BillPlz bill ID',
        };
      }

      const billResult = await this.getBillPlzClient().getBill(
        paymentRecord.billplz_bill_id,
      );

      if (!billResult.success || !billResult.data) {
        return {
          data: null,
          error: billResult.error || 'Failed to fetch bill from BillPlz',
        };
      }

      const bill = billResult.data;

      // Determine payment status based on BillPlz bill state
      let paymentStatus: PaymentStatus;
      if (bill.paid && bill.state === 'paid') {
        paymentStatus = 'paid';
      } else if (bill.state === 'overdue') {
        paymentStatus = 'failed';
      } else {
        paymentStatus = 'pending';
      }

      // Update payment status if it has changed
      if (paymentStatus !== paymentRecord.status) {
        const updateParams: PaymentStatusUpdate = {
          paymentRecordId,
          status: paymentStatus,
          billPlzData: bill,
          paidAmount: bill.paid_amount > 0 ? bill.paid_amount : undefined,
          paidAt: bill.paid_at || undefined,
        };

        return await this.updatePaymentStatus(updateParams);
      }

      // No update needed
      return {
        data: {
          paymentRecord,
          subscriptionUpdated: false,
          accessRestored: false,
        },
        error: null,
      };
    } catch (error) {
      console.error('Sync payment with BillPlz error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async retryFailedPayment(paymentRecordId: string): Promise<{
    data: { paymentRecord: PaymentRecord; billUrl: string } | null;
    error: string | null;
  }> {
    try {
      // Get payment record with subscription details
      const { data: paymentRecord, error: fetchError } = await supabase
        .from('payment_records')
        .select(
          `
          *,
          project_subscriptions!inner(*)
        `,
        )
        .eq('id', paymentRecordId)
        .single();

      if (fetchError || !paymentRecord) {
        return {
          data: null,
          error: fetchError?.message || 'Payment record not found',
        };
      }

      // Check if payment can be retried
      if (paymentRecord.status !== 'failed') {
        return {
          data: null,
          error: 'Payment is not in failed status',
        };
      }

      // Create new BillPlz bill for retry
      const amount = convertCentsToMyr(paymentRecord.amount);
      const subscription = (paymentRecord as PaymentRecordJoined)
        .project_subscriptions;

      const billResult = await this.getBillPlzClient().createBill({
        contractorEmail: '<EMAIL>', // TODO: Get from subscription/user
        contractorName: 'Payment Retry', // TODO: Get from subscription/user
        amount, // BillPlz client handles MYR to cents conversion
        description: `Payment Retry for Project ${subscription.project_id}`,
        projectId: subscription.project_id,
        subscriptionId: subscription.id,
        callbackUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/billing/webhook`,
        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/projects/${subscription.project_id}`,
      });

      if (!billResult.success || !billResult.data) {
        return {
          data: null,
          error: billResult.error || 'Failed to create retry bill',
        };
      }

      const bill = billResult.data;

      // Update payment record with new BillPlz bill
      const { data: updatedPayment, error: updateError } = await supabase
        .from('payment_records')
        .update({
          billplz_bill_id: bill.id,
          billplz_response: bill as unknown as Json, // BillPlz response as Json for Supabase
          status: 'pending',
          failure_reason: null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', paymentRecordId)
        .select()
        .single();

      if (updateError || !updatedPayment) {
        // Clean up BillPlz bill if database update fails
        await this.getBillPlzClient().deleteBill(bill.id);
        return {
          data: null,
          error: updateError?.message || 'Failed to update payment record',
        };
      }

      return {
        data: {
          paymentRecord: updatedPayment,
          billUrl: bill.url,
        },
        error: null,
      };
    } catch (error) {
      console.error('Retry failed payment error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getPaymentsBySubscription(subscriptionId: string): Promise<{
    data: PaymentRecord[];
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('payment_records')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to get payments by subscription:', error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Get payments by subscription error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getPaymentWithSubscription(paymentRecordId: string): Promise<{
    data: PaymentRecordWithSubscription | null;
    error: string | null;
  }> {
    try {
      const { data: paymentRecord, error } = await supabase
        .from('payment_records')
        .select(
          `
          *,
          project_subscriptions!inner(
            *,
            projects!inner(name)
          )
        `,
        )
        .eq('id', paymentRecordId)
        .single();

      if (error) {
        console.error('Failed to get payment with subscription:', error);
        return { data: null, error: error.message };
      }

      // Transform the joined data
      const subscriptionWithProject = (paymentRecord as PaymentRecordJoined)
        .project_subscriptions;
      const { projects: project, ...subscription } = subscriptionWithProject;

      const paymentWithSubscription: PaymentRecordWithSubscription = {
        ...paymentRecord,
        subscription,
        projectName: project?.name,
      };

      return { data: paymentWithSubscription, error: null };
    } catch (error) {
      console.error('Get payment with subscription error:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getContractorPaymentHistory(contractorId: string): Promise<{
    data: PaymentRecordWithSubscription[];
    error: string | null;
  }> {
    try {
      const { data: payments, error } = await supabase
        .from('payment_records')
        .select(
          `
          *,
          project_subscriptions!inner(
            *,
            projects!inner(name)
          )
        `,
        )
        .eq('project_subscriptions.contractor_id', contractorId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to get contractor payment history:', error);
        return { data: [], error: error.message };
      }

      // Transform the joined data
      const paymentsWithSubscription: PaymentRecordWithSubscription[] = (
        payments || []
      ).map((payment: PaymentRecordJoined) => {
        const { projects: project, ...subscription } =
          payment.project_subscriptions;
        return {
          ...payment,
          subscription,
          projectName: project?.name,
        };
      });

      return { data: paymentsWithSubscription, error: null };
    } catch (error) {
      console.error('Get contractor payment history error:', error);
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async handleFailedPayment(
    paymentRecordId: string,
    failureReason?: string,
  ): Promise<{ success: boolean; error: string | null }> {
    try {
      const updateResult = await this.updatePaymentStatus({
        paymentRecordId,
        status: 'failed',
        failureReason: failureReason || 'Payment failed',
      });

      if (updateResult.error) {
        return { success: false, error: updateResult.error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Handle failed payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async processSuccessfulPayment(
    paymentRecordId: string,
    billPlzData: BillPlzBill,
  ): Promise<{ success: boolean; error: string | null }> {
    try {
      const updateResult = await this.updatePaymentStatus({
        paymentRecordId,
        status: 'paid',
        billPlzData,
        paidAmount: billPlzData.paid_amount,
        paidAt: billPlzData.paid_at || new Date().toISOString(),
      });

      if (updateResult.error) {
        return { success: false, error: updateResult.error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Process successful payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getBillPlzPaymentUrl(paymentRecordId: string): Promise<{
    url: string | null;
    error: string | null;
  }> {
    try {
      const { data: paymentRecord, error } = await supabase
        .from('payment_records')
        .select('billplz_response')
        .eq('id', paymentRecordId)
        .single();

      if (error || !paymentRecord) {
        return {
          url: null,
          error: error?.message || 'Payment record not found',
        };
      }

      const billData = paymentRecord.billplz_response as unknown as BillPlzBill;

      if (!billData?.url) {
        return {
          url: null,
          error: 'BillPlz payment URL not found',
        };
      }

      return { url: billData.url, error: null };
    } catch (error) {
      console.error('Get BillPlz payment URL error:', error);
      return {
        url: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export const paymentsService = new PaymentsService();
