import { supabase } from '@/lib/supabase';
import type { BillPlzBill } from '@/types/billing';
import type { Database } from '@/types/database';
import { createHash, timingSafeEqual } from 'crypto';
import { z } from 'zod';
import { paymentsService } from './payments.service';

// BillPlz webhook payload schema
export const billPlzWebhookSchema = z.object({
  id: z.string(),
  collection_id: z.string(),
  paid: z.boolean(),
  state: z.enum(['overdue', 'paid']),
  amount: z.number(),
  paid_amount: z.number().optional(),
  due_at: z.string(),
  email: z.string().email(),
  mobile: z.string().optional(),
  name: z.string(),
  url: z.string().url(),
  reference_1_label: z.string().optional(),
  reference_1: z.string().optional(),
  reference_2_label: z.string().optional(),
  reference_2: z.string().optional(),
  redirect_url: z.string().url().optional(),
  callback_url: z.string().url().optional(),
  description: z.string().optional(),
  paid_at: z.string().optional(),
});

// Type for limited joins in webhook context (matches actual query)
type PaymentRecordWithLimitedJoins =
  Database['public']['Tables']['payment_records']['Row'] & {
    project_subscriptions: Database['public']['Tables']['project_subscriptions']['Row'] & {
      projects: {
        id: string;
        name: string;
      };
      users: {
        id: string;
        email: string;
        user_role: string;
      };
    };
  };

interface WebhookContext {
  paymentRecord?: Database['public']['Tables']['payment_records']['Row'];
  subscription?: Database['public']['Tables']['project_subscriptions']['Row'];
  project?: {
    id: string;
    name: string;
  };
  contractor?: {
    id: string;
    email: string;
    user_role: string;
  };
}

export interface WebhookProcessingResult {
  success: boolean;
  error?: string;
  accessRestored: boolean;
  paymentRecordId?: string;
  subscriptionId?: string;
  projectId?: string;
  contractorId?: string;
  billState?: string;
}

export class WebhookService {
  /**
   * Verify BillPlz webhook signature
   */
  static verifyBillPlzSignature(
    body: string,
    signature: string | null,
  ): boolean {
    if (!signature) {
      console.warn('No signature provided in BillPlz webhook');
      return process.env.NODE_ENV === 'development'; // Allow in development
    }

    const webhookSigningKey = process.env.BILLPLZ_WEBHOOK_SIGNING_KEY;
    if (!webhookSigningKey) {
      console.warn('BillPlz webhook signing key not configured');
      return process.env.NODE_ENV === 'development'; // Allow in development
    }

    try {
      const expectedSignature = createHash('sha256')
        .update(webhookSigningKey + body)
        .digest('hex');

      // Remove 'sha256=' prefix if present
      const cleanSignature = signature.replace('sha256=', '');

      return timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(cleanSignature, 'hex'),
      );
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Process BillPlz webhook payload
   */
  static async processBillPlzWebhook(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): Promise<WebhookProcessingResult> {
    try {
      // Find payment record by BillPlz bill ID with all necessary joins
      const context = await this.getWebhookContext(webhookData.id);
      if (!context.paymentRecord) {
        return {
          success: false,
          error: `Payment record not found for BillPlz bill: ${webhookData.id}`,
          accessRestored: false,
        };
      }

      console.log(
        `BillPlz webhook received for bill ${webhookData.id}: ${webhookData.state}`,
      );

      // Process webhook based on payment state
      let webhookResult: {
        success: boolean;
        error?: string;
        accessRestored: boolean;
      };

      if (webhookData.paid && webhookData.state === 'paid') {
        webhookResult = await this.handleSuccessfulPayment(
          webhookData,
          context,
        );
      } else if (webhookData.state === 'overdue') {
        webhookResult = await this.handleFailedPayment(webhookData, context);
      } else {
        // Handle other states (pending, etc.)
        webhookResult = await this.handlePendingPayment(webhookData, context);
      }

      if (!webhookResult.success) {
        return {
          success: false,
          error: webhookResult.error,
          accessRestored: false,
        };
      }

      // Log successful webhook processing for audit trail
      console.log(
        `BillPlz webhook processed successfully for payment ${context.paymentRecord.id}, access restored: ${webhookResult.accessRestored}`,
      );

      return {
        success: true,
        accessRestored: webhookResult.accessRestored,
        paymentRecordId: context.paymentRecord.id,
        subscriptionId: context.subscription?.id,
        projectId: context.project?.id,
        contractorId: context.contractor?.id,
        billState: webhookData.state,
      };
    } catch (error) {
      console.error('Webhook processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Get webhook context by fetching payment record with joins
   */
  private static async getWebhookContext(
    billId: string,
  ): Promise<WebhookContext> {
    const { data: paymentRecord, error: paymentError } = await supabase
      .from('payment_records')
      .select(
        `
        *,
        project_subscriptions!inner(
          *,
          projects!inner(id, name),
          users!inner(id, email, user_role)
        )
      `,
      )
      .eq('billplz_bill_id', billId)
      .single();

    if (paymentError || !paymentRecord) {
      return {};
    }

    return {
      paymentRecord,
      subscription: (paymentRecord as unknown as PaymentRecordWithLimitedJoins)
        .project_subscriptions,
      project: (paymentRecord as unknown as PaymentRecordWithLimitedJoins)
        .project_subscriptions.projects,
      contractor: (paymentRecord as unknown as PaymentRecordWithLimitedJoins)
        .project_subscriptions.users,
    };
  }

  /**
   * Handle successful payment webhook
   */
  private static async handleSuccessfulPayment(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
    context: WebhookContext,
  ): Promise<{ success: boolean; error?: string; accessRestored: boolean }> {
    try {
      if (!context.paymentRecord) {
        return {
          success: false,
          error: 'Payment record not found in context',
          accessRestored: false,
        };
      }

      // Update payment record to paid status
      const updateResult = await paymentsService.updatePaymentStatus({
        paymentRecordId: context.paymentRecord.id,
        status: 'paid',
        billPlzData: this.webhookToBillPlzBill(webhookData),
        paidAmount: webhookData.paid_amount || webhookData.amount,
        paidAt: webhookData.paid_at || new Date().toISOString(),
      });

      if (updateResult.error) {
        return {
          success: false,
          error: updateResult.error,
          accessRestored: false,
        };
      }

      // Access should be automatically restored via the payment service
      const accessRestored = updateResult.data?.accessRestored || false;

      // Send notification (future enhancement)
      await this.sendPaymentSuccessNotification(context, webhookData);

      return { success: true, accessRestored };
    } catch (error) {
      console.error('Handle successful payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Handle failed payment webhook
   */
  private static async handleFailedPayment(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
    context: WebhookContext,
  ): Promise<{ success: boolean; error?: string; accessRestored: boolean }> {
    try {
      if (!context.paymentRecord) {
        return {
          success: false,
          error: 'Payment record not found in context',
          accessRestored: false,
        };
      }

      // Update payment record to failed status
      const updateResult = await paymentsService.updatePaymentStatus({
        paymentRecordId: context.paymentRecord.id,
        status: 'failed',
        billPlzData: this.webhookToBillPlzBill(webhookData),
        failureReason: 'Payment overdue - BillPlz webhook',
      });

      if (updateResult.error) {
        return {
          success: false,
          error: updateResult.error,
          accessRestored: false,
        };
      }

      // Subscription should transition to grace period via the payment service

      // Send notification (future enhancement)
      await this.sendPaymentFailureNotification(context, webhookData);

      return { success: true, accessRestored: false };
    } catch (error) {
      console.error('Handle failed payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Handle pending payment webhook
   */
  private static async handlePendingPayment(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
    context: WebhookContext,
  ): Promise<{ success: boolean; error?: string; accessRestored: boolean }> {
    try {
      // Update payment record with latest BillPlz data
      if (!context.paymentRecord) {
        return {
          success: false,
          error: 'Payment record not found in context',
          accessRestored: false,
        };
      }

      const { error: updateError } = await supabase
        .from('payment_records')
        .update({
          billplz_response: this.webhookToBillPlzBill(
            webhookData,
          ) as unknown as Database['public']['Tables']['payment_records']['Update']['billplz_response'],
          updated_at: new Date().toISOString(),
        })
        .eq('id', context.paymentRecord.id);

      if (updateError) {
        return {
          success: false,
          error: updateError.message,
          accessRestored: false,
        };
      }

      return { success: true, accessRestored: false };
    } catch (error) {
      console.error('Handle pending payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        accessRestored: false,
      };
    }
  }

  /**
   * Convert webhook data to BillPlzBill type
   */
  private static webhookToBillPlzBill(
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): BillPlzBill {
    return {
      id: webhookData.id,
      collection_id: webhookData.collection_id,
      paid: webhookData.paid,
      state: webhookData.state,
      amount: webhookData.amount,
      paid_amount: webhookData.paid_amount || 0,
      due_at: webhookData.due_at,
      email: webhookData.email,
      mobile: webhookData.mobile || null,
      name: webhookData.name,
      url: webhookData.url,
      reference_1_label: webhookData.reference_1_label || null,
      reference_1: webhookData.reference_1 || null,
      reference_2_label: webhookData.reference_2_label || null,
      reference_2: webhookData.reference_2 || null,
      redirect_url: webhookData.redirect_url || null,
      callback_url: webhookData.callback_url || '',
      description: webhookData.description || '',
      paid_at: webhookData.paid_at || null,
    };
  }

  /**
   * Send payment success notification
   */
  private static async sendPaymentSuccessNotification(
    context: WebhookContext,
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): Promise<void> {
    // Ensure required context exists
    if (!context.contractor || !context.project) {
      console.warn(
        'Missing contractor or project context for payment success notification',
      );
      return;
    }

    // Future enhancement: Send email/SMS notifications
    console.log(
      `Payment success notification: ${context.contractor.email} paid for project ${context.project.name}`,
    );

    // Could integrate with email service, push notifications, etc.
    // Example notification data:
    const notificationData = {
      type: 'payment_success',
      contractorEmail: context.contractor.email,
      contractorName: webhookData.name,
      projectName: context.project.name,
      amount: webhookData.paid_amount || webhookData.amount,
      paidAt: webhookData.paid_at,
      billId: webhookData.id,
    };

    // Log for now, implement notification service later
    console.log('Payment success notification data:', notificationData);
  }

  /**
   * Send payment failure notification
   */
  private static async sendPaymentFailureNotification(
    context: WebhookContext,
    webhookData: z.infer<typeof billPlzWebhookSchema>,
  ): Promise<void> {
    // Ensure required context exists
    if (!context.contractor || !context.project) {
      console.warn(
        'Missing contractor or project context for payment failure notification',
      );
      return;
    }

    // Future enhancement: Send email/SMS notifications
    console.log(
      `Payment failure notification: ${context.contractor.email} payment failed for project ${context.project.name}`,
    );

    // Could integrate with email service, push notifications, etc.
    // Example notification data:
    const notificationData = {
      type: 'payment_failure',
      contractorEmail: context.contractor.email,
      contractorName: webhookData.name,
      projectName: context.project.name,
      amount: webhookData.amount,
      dueAt: webhookData.due_at,
      billId: webhookData.id,
      gracePeriodMessage:
        'Your subscription has entered a grace period. Please complete payment to maintain access.',
    };

    // Log for now, implement notification service later
    console.log('Payment failure notification data:', notificationData);
  }
}

export const webhookService = new WebhookService();
