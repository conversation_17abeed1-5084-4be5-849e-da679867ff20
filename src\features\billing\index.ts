// ================================
// BILLING FEATURE EXPORTS
// ================================

// Components
export {
  AccessStateManager,
  PaymentForm,
  ProjectAccessIndicator,
  SubscriptionCard,
  ContractorProjectOverview,
  ProjectPmaCard,
} from './components';

export type {
  AccessState,
  AccessStateManagerProps,
  PaymentFormProps,
  ProjectAccessIndicatorProps,
  SubscriptionCardProps,
  ContractorProjectOverviewProps,
  ProjectPmaCardProps,
} from './components';

// Hook types
export type {
  AccessDeniedReason,
  ProjectCreationFlowState,
  ProjectCreationResult,
  ProjectCreationStep,
  ProjectWithBilling,
} from './hooks';

// Hooks
export {
  BILLING_QUERY_KEYS,
  BILLING_QUERY_OPTIONS,
  useAccessGuard,
  useActiveBillPlzBills,
  // Query utilities
  useBillingQueryK<PERSON>s,
  useBillPlzBillDetails,
  useBillPlzHealthCheck,
  useCancelBillPlzBill,
  useCancelSubscription,
  useCanCreateProject,
  // BillPlz
  useCheckPaymentStatus,
  // Project Access
  useCheckProjectAccess,
  useContractorPaymentHistory,
  // Subscriptions
  useContractorSubscriptions,
  useCreateBillForSubscription,
  useCreatePaymentRecord,
  // Project Creation
  useCreateProjectWithBilling,
  useCreateSubscriptionForProject,
  useInvalidateProjectAccess,
  useMultipleProjectsAccess,
  useOverduePayments,
  usePaymentDetails,
  usePaymentStats,
  useProjectCreationAnalytics,
  useProjectCreationCost,
  useProjectCreationFlow,
  useProjectCreationNavigation,
  useProjectSubscription,
  useProjectSubscriptionStatus,
  useReactivateSubscription,
  useRecentPayments,
  useRetriablePayments,
  useRetryBillPlzPayment,
  useRetryFailedPayment,
  // Payment Records
  useSubscriptionPaymentHistory,
  useSubscriptionsRequiringAttention,
  useSubscriptionStats,
  useSuspendSubscription,
  useTransitionToGracePeriod,
  useUpdatePaymentRecord,
  useUpdateSubscriptionStatus,
} from './hooks';

// Services - explicit exports to avoid conflicts
export {
  accessControlService,
  paymentsService,
  projectIntegrationService,
  subscriptionsService,
} from './services';

// Re-export commonly used types
export type {
  BillingSummary,
  BillPlzBill,
  PaymentFormData,
  PaymentRecord,
  PaymentStatus,
  ProjectSubscription,
  SubscriptionStatus,
  SubscriptionWithAccess,
} from '@/types/billing';

// Re-export new project-grouped billing types
export type {
  ContractorBillingData,
  ProjectWithPmas,
  BillingStats,
  ProjectPmaSummary,
  ProjectBillingFilters,
} from './types';

// Export project grouping utilities
export {
  groupSubscriptionsByProject,
  calculateBillingStats,
} from './utils/project-grouping';
