'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { SubscriptionWithAccess } from '@/types/billing';
import { getDaysRemainingInGracePeriod } from '@/types/billing';
import {
  AlertTriangle,
  Ban,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  ExternalLink,
  RefreshCw,
  Settings,
} from 'lucide-react';
import { useState } from 'react';
import { AccessStateManager } from './AccessStateManager';
import { ManagePaymentModal } from './ManagePaymentModal';
import { ProjectAccessIndicator } from './ProjectAccessIndicator';

export interface SubscriptionCardProps {
  subscription: SubscriptionWithAccess;
  projectName?: string;
  projectId?: string;
  onPayNow?: (subscriptionId: string) => void;
  onViewProject?: (projectId: string) => void;
  _onManageSubscription?: (subscriptionId: string) => void;
  onCancel?: (subscriptionId: string) => void;
  onReactivate?: (subscriptionId: string) => void;
  onUpdatePaymentMethod?: (subscriptionId: string) => void;
  onViewPaymentHistory?: (subscriptionId: string) => void;
  onUpdateBilling?: (subscriptionId: string) => void;
  onDownloadReceipt?: (subscriptionId: string) => void;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
}

/**
 * Enhanced subscription display component with access status integration
 */
export function SubscriptionCard({
  subscription,
  projectName,
  projectId,
  onPayNow,
  onViewProject,
  _onManageSubscription,
  onCancel,
  onReactivate,
  onUpdatePaymentMethod,
  onViewPaymentHistory,
  onUpdateBilling,
  onDownloadReceipt,
  className,
  variant = 'default',
  showActions = true,
}: SubscriptionCardProps) {
  const [showManageModal, setShowManageModal] = useState(false);
  const daysRemaining = getDaysRemainingInGracePeriod(
    subscription.grace_period_ends,
  );
  const monthlyAmount = subscription.amount || 0;
  const isUrgent =
    subscription.isInGracePeriod &&
    (daysRemaining === null || daysRemaining <= 2);

  if (variant === 'compact') {
    return (
      <Card className={cn('transition-all hover:shadow-md', className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <ProjectAccessIndicator
                status={subscription.status}
                gracePeriodEnds={subscription.grace_period_ends}
                showLabel={false}
                size="sm"
              />
              <div>
                <h4 className="font-medium text-sm">
                  {projectName || 'Unnamed Project'}
                </h4>
                <p className="text-xs text-muted-foreground">
                  RM {monthlyAmount.toFixed(2)}/month
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isUrgent && (
                <Badge variant="destructive" className="text-xs">
                  Urgent
                </Badge>
              )}
              {showActions && renderQuickActions()}
            </div>
          </div>
        </CardContent>

        {/* Manage Payment Modal */}
        <ManagePaymentModal
          subscription={subscription}
          projectName={projectName}
          open={showManageModal}
          onOpenChange={setShowManageModal}
          onUpdatePaymentMethod={onUpdatePaymentMethod}
          onViewPaymentHistory={onViewPaymentHistory}
          onUpdateBilling={onUpdateBilling}
          onCancelSubscription={onCancel}
          _onDownloadReceipt={onDownloadReceipt}
        />
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card
        className={cn(
          'transition-all hover:shadow-md',
          getCardStyling(),
          className,
        )}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-lg flex items-center gap-2">
                {projectName || 'Unnamed Project'}
                {isUrgent && (
                  <Badge variant="destructive" className="text-xs">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Urgent
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Monthly subscription • RM {monthlyAmount.toFixed(2)}
              </CardDescription>
            </div>
            <ProjectAccessIndicator
              status={subscription.status}
              gracePeriodEnds={subscription.grace_period_ends}
              size="sm"
            />
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Access State Manager */}
          <AccessStateManager
            subscription={subscription}
            projectName={projectName}
            onPayNow={() => onPayNow?.(subscription.id)}
            onReactivate={() => onReactivate?.(subscription.id)}
            onCancel={() => onCancel?.(subscription.id)}
            variant="compact"
            className="bg-muted/30"
          />

          {/* Grace Period Progress */}
          {subscription.isInGracePeriod && daysRemaining !== null && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Grace Period</span>
                <span
                  className={cn(
                    'font-medium',
                    daysRemaining <= 1
                      ? 'text-red-600'
                      : daysRemaining <= 3
                        ? 'text-yellow-600'
                        : 'text-blue-600',
                  )}
                >
                  {daysRemaining > 0 ? `${daysRemaining} days left` : 'Expired'}
                </span>
              </div>
              <Progress
                value={
                  daysRemaining > 0 ? ((7 - daysRemaining) / 7) * 100 : 100
                }
                className={cn(
                  'h-2',
                  daysRemaining <= 1 && '[&>div]:bg-red-500',
                  daysRemaining <= 3 && '[&>div]:bg-yellow-500',
                )}
              />
            </div>
          )}

          {/* Subscription Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground mb-1">Status</p>
              <Badge variant="outline" className="text-xs">
                {subscription.status.replace('_', ' ')}
              </Badge>
            </div>
            <div>
              <p className="text-muted-foreground mb-1">Access</p>
              <div className="flex items-center gap-1">
                {subscription.accessAllowed ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <Ban className="h-3 w-3 text-red-500" />
                )}
                <span className="text-xs">
                  {subscription.accessAllowed ? 'Allowed' : 'Blocked'}
                </span>
              </div>
            </div>
          </div>

          {/* Next Billing Date */}
          {subscription.next_billing_date &&
            subscription.status === 'active' && (
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Next billing:</span>
                <span className="font-medium">
                  {new Date(
                    subscription.next_billing_date,
                  ).toLocaleDateString()}
                </span>
              </div>
            )}

          {/* Action Buttons */}
          {showActions && (
            <>
              <Separator />
              <div className="flex flex-wrap gap-2">
                {renderActionButtons()}
              </div>
            </>
          )}
        </CardContent>

        {/* Manage Payment Modal */}
        <ManagePaymentModal
          subscription={subscription}
          projectName={projectName}
          open={showManageModal}
          onOpenChange={setShowManageModal}
          onUpdatePaymentMethod={onUpdatePaymentMethod}
          onViewPaymentHistory={onViewPaymentHistory}
          onUpdateBilling={onUpdateBilling}
          onCancelSubscription={onCancel}
          _onDownloadReceipt={onDownloadReceipt}
        />
      </Card>
    );
  }

  // Default variant
  return (
    <Card
      className={cn(
        'transition-all hover:shadow-md',
        getCardStyling(),
        className,
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-base flex items-center gap-2">
              {projectName || 'Unnamed Project'}
              {isUrgent && (
                <Badge variant="destructive" className="text-xs">
                  Urgent
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              RM {monthlyAmount.toFixed(2)}/month
            </CardDescription>
          </div>
          <ProjectAccessIndicator
            status={subscription.status}
            gracePeriodEnds={subscription.grace_period_ends}
            size="sm"
          />
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3">
          {/* Grace Period Warning */}
          {subscription.isInGracePeriod && (
            <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div className="flex-1 text-sm">
                <p className="font-medium text-yellow-800 dark:text-yellow-200">
                  Payment Required
                </p>
                <p className="text-xs text-yellow-700 dark:text-yellow-300">
                  {daysRemaining !== null && daysRemaining > 0
                    ? `${daysRemaining} day${daysRemaining !== 1 ? 's' : ''} until access suspension`
                    : 'Grace period has ended'}
                </p>
              </div>
            </div>
          )}

          {/* Quick Info */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Access Status</span>
            <span
              className={cn(
                'font-medium',
                subscription.accessAllowed ? 'text-green-600' : 'text-red-600',
              )}
            >
              {subscription.accessAllowed ? 'Active' : 'Blocked'}
            </span>
          </div>

          {/* Action Buttons */}
          {showActions && (
            <div className="flex gap-2 pt-2">{renderQuickActions()}</div>
          )}
        </div>
      </CardContent>

      {/* Manage Payment Modal */}
      <ManagePaymentModal
        subscription={subscription}
        projectName={projectName}
        open={showManageModal}
        onOpenChange={setShowManageModal}
        onUpdatePaymentMethod={onUpdatePaymentMethod}
        onViewPaymentHistory={onViewPaymentHistory}
        onUpdateBilling={onUpdateBilling}
        onCancelSubscription={onCancel}
        _onDownloadReceipt={onDownloadReceipt}
      />
    </Card>
  );

  function getCardStyling() {
    if (isUrgent) {
      return 'border-red-200 dark:border-red-800 shadow-red-100 dark:shadow-red-900/20';
    }
    if (subscription.isInGracePeriod) {
      return 'border-yellow-200 dark:border-yellow-800 shadow-yellow-100 dark:shadow-yellow-900/20';
    }
    if (subscription.accessAllowed) {
      return 'border-green-200 dark:border-green-800';
    }
    return 'border-gray-200 dark:border-gray-800';
  }

  function renderQuickActions() {
    const actions = [];

    // Pay Now - highest priority for grace period
    if (subscription.isInGracePeriod && onPayNow) {
      actions.push(
        <Button
          key="pay"
          size="sm"
          variant={isUrgent ? 'destructive' : 'default'}
          onClick={() => onPayNow(subscription.id)}
          className={cn(isUrgent && 'bg-red-600 hover:bg-red-700')}
        >
          <CreditCard className="h-3 w-3 mr-1" />
          Pay Now
        </Button>,
      );
    }

    // View Project
    if (projectId && onViewProject) {
      actions.push(
        <Button
          key="view"
          size="sm"
          variant="outline"
          onClick={() => onViewProject(projectId)}
        >
          <ExternalLink className="h-3 w-3 mr-1" />
          View
        </Button>,
      );
    }

    // Manage for non-urgent states
    if (!isUrgent) {
      actions.push(
        <Button
          key="manage"
          size="sm"
          variant="ghost"
          onClick={() => setShowManageModal(true)}
        >
          <Settings className="h-3 w-3 mr-1" />
          Manage
        </Button>,
      );
    }

    return actions;
  }

  function renderActionButtons() {
    const actions = [];

    // Primary actions based on status
    if (subscription.isInGracePeriod && onPayNow) {
      actions.push(
        <Button
          key="pay"
          variant={isUrgent ? 'destructive' : 'default'}
          onClick={() => onPayNow(subscription.id)}
          className={cn(isUrgent && 'bg-red-600 hover:bg-red-700')}
        >
          <CreditCard className="h-4 w-4 mr-2" />
          {isUrgent ? 'Pay Now (Urgent)' : 'Pay Now'}
        </Button>,
      );
    }

    // Reactivate for cancelled/suspended
    if (
      (subscription.status === 'cancelled' ||
        subscription.status === 'suspended') &&
      onReactivate
    ) {
      actions.push(
        <Button
          key="reactivate"
          variant="default"
          onClick={() => onReactivate(subscription.id)}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Reactivate
        </Button>,
      );
    }

    // Secondary actions
    if (projectId && onViewProject) {
      actions.push(
        <Button
          key="view"
          variant="outline"
          onClick={() => onViewProject(projectId)}
        >
          <ExternalLink className="h-4 w-4 mr-2" />
          View Project
        </Button>,
      );
    }

    actions.push(
      <Button
        key="manage"
        variant="ghost"
        onClick={() => setShowManageModal(true)}
      >
        <Settings className="h-4 w-4 mr-2" />
        Manage
      </Button>,
    );

    // Cancel action for active subscriptions
    if (subscription.status === 'active' && onCancel) {
      actions.push(
        <Button
          key="cancel"
          variant="outline"
          onClick={() => onCancel(subscription.id)}
          className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
        >
          <Ban className="h-4 w-4 mr-2" />
          Cancel
        </Button>,
      );
    }

    return actions;
  }
}
