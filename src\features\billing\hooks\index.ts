// ================================
// PROJECT ACCESS HOOKS
// ================================

export {
  useCheckProjectAccess,
  useProjectSubscriptionStatus,
  useAccessGuard,
  useInvalidateProjectAccess,
  useMultipleProjectsAccess,
  type AccessState,
  type AccessDeniedReason,
} from './useProjectAccess';

// ================================
// SUBSCRIPTION HOOKS
// ================================

export {
  useContractorSubscriptions,
  useProjectSubscription,
  useSubscriptionStats,
  useCreateSubscriptionForProject,
  useUpdateSubscriptionStatus,
  useCancelSubscription,
  useReactivateSubscription,
  useTransitionToGracePeriod,
  useSuspendSubscription,
  useSubscriptionsRequiringAttention,
} from './useSubscriptions';

// ================================
// PAYMENT RECORD HOOKS
// ================================

export {
  useSubscriptionPaymentHistory,
  useContractorPaymentHistory,
  usePaymentDetails,
  useRetriablePayments,
  usePaymentStats,
  useCreatePaymentRecord,
  useUpdatePaymentRecord,
  useRetryFailedPayment,
  useRecentPayments,
  useOverduePayments,
} from './usePaymentRecords';

// ================================
// BILLPLZ HOOKS
// ================================

export {
  useCheckPaymentStatus,
  useBillPlzBillDetails,
  useCreateBillForSubscription,
  useCancelBillPlzBill,
  useRetryBillPlzPayment,
  useActiveBillPlzBills,
  useBillPlzHealthCheck,
} from './useBillPlz';

// ================================
// PROJECT CREATION HOOKS
// ================================

export {
  useCreateProjectWithBilling,
  useProjectCreationFlow,
  useProjectCreationNavigation,
  useCanCreateProject,
  useProjectCreationCost,
  useProjectCreationAnalytics,
  type ProjectCreationStep,
  type ProjectWithBilling,
  type ProjectCreationResult,
  type ProjectCreationFlowState,
} from './useProjectCreation';

// ================================
// COMMON PATTERNS & UTILITIES
// ================================

/**
 * Common query options for billing-related queries
 */
export const BILLING_QUERY_OPTIONS = {
  // Standard stale times for different types of data
  SUBSCRIPTION_STATUS: 30 * 1000, // 30 seconds
  PAYMENT_RECORDS: 2 * 60 * 1000, // 2 minutes
  PROJECT_ACCESS: 30 * 1000, // 30 seconds
  BILLING_STATS: 5 * 60 * 1000, // 5 minutes
  BILLPLZ_DATA: 2 * 60 * 1000, // 2 minutes

  // Refetch intervals
  PAYMENT_STATUS_POLLING: 60 * 1000, // 1 minute
  ACCESS_MONITORING: 60 * 1000, // 1 minute
} as const;

/**
 * Common query key patterns for consistency
 */
export const BILLING_QUERY_KEYS = {
  // Project access
  projectAccess: (
    projectId: string,
    contractorId?: string,
    userId?: string,
  ) => ['project-access', projectId, contractorId, userId],

  // Subscriptions
  contractorSubscriptions: (contractorId: string) => [
    'contractor-subscriptions',
    contractorId,
  ],
  projectSubscription: (projectId: string, contractorId: string) => [
    'project-subscription',
    projectId,
    contractorId,
  ],

  // Payment records
  subscriptionPaymentHistory: (
    subscriptionId: string,
    options?: Record<string, unknown>,
  ) => [
    'subscription-payment-history',
    subscriptionId,
    ...(options ? [options] : []),
  ],
  contractorPaymentHistory: (
    contractorId: string,
    options?: Record<string, unknown>,
  ) => [
    'contractor-payment-history',
    contractorId,
    ...(options ? [options] : []),
  ],

  // BillPlz
  billPlzBill: (billId: string) => ['billplz-bill-details', billId],
  billPlzPaymentStatus: (billId: string, paymentRecordId: string) => [
    'billplz-payment-status',
    billId,
    paymentRecordId,
  ],
} as const;

/**
 * Hook to get all billing-related query keys for invalidation
 */
export function useBillingQueryKeys(contractorId?: string, userId?: string) {
  return {
    // Invalidate all billing queries
    invalidateAll: () => [
      ['project-access'],
      ['contractor-subscriptions'],
      ['project-subscription'],
      ['subscription-payment-history'],
      ['contractor-payment-history'],
      ['billplz-bill-details'],
      ['billplz-payment-status'],
    ],

    // Invalidate contractor-specific queries
    invalidateContractor: (cId: string = contractorId || '') => [
      ['contractor-subscriptions', cId],
      ['contractor-payment-history', cId],
      ['subscription-stats', cId],
      ['payment-stats', cId],
    ],

    // Invalidate project-specific queries
    invalidateProject: (
      projectId: string,
      cId: string = contractorId || '',
    ) => [
      ['project-access', projectId],
      ['project-subscription', projectId, cId],
      ['project-subscription-status', projectId, cId],
    ],

    // Invalidate user access queries
    invalidateUserAccess: (uId: string = userId || '') => [
      ['project-access', undefined, undefined, uId],
      ['multiple-projects-access', undefined, undefined, uId],
    ],
  };
}
